# 医疗病历质控规范

## 文档概述

本文档规范了医疗病历质控系统的各项标准、流程和评估规则，适用于出院病历的自动化质量控制检查。

---

## 1. 质控流程设计

### 1.1 总体流程

出院病历质控遵循以下七个核心步骤：

1. **基础信息完整性校验**（代码判断）
2. **诊断逻辑性校验**（LLM判断）
3. **临床信息质量校验**（LLM判断）
4. **治疗过程校验**（LLM判断）
5. **出院管理校验**（LLM+代码判断）
6. **特殊标记校验**（代码+LLM判断）
7. **跨要素一致性校验**（LLM判断）

### 1.2 权重分配

| 质控项目 | 权重 | 备注 |
|---------|------|------|
| 基础信息完整性 | 15% | 必填字段检查 |
| 诊断逻辑性 | 20% | 诊断演进合理性 |
| 临床信息质量 | 20% | 症状、检查、会诊记录 |
| 治疗过程 | 20% | 病程、疗效、并发症 |
| 出院管理 | 15% | 出院情况、用药建议 |
| 肿瘤标记一致性 | 5% | 肿瘤标记与诊断一致性 |
| 跨要素一致性 | 5% | 各部分内在逻辑一致性 |

---

## 2. 评估标准详述

### 2.1 基础信息完整性校验

#### 必填字段要求
- 病区：必须填写
- 床位号：必须填写
- 门诊号：必须填写
- 住院号：必须填写

#### 校验规则
- 字段不能为空
- 字段不能仅包含空白字符
- 缺少任何必填字段即判定为严重错误

### 2.2 诊断逻辑性校验

#### 评估维度
1. **诊断演进逻辑性**
   - 门诊诊断→入院诊断→出院诊断应有合理演进逻辑
   - 新增诊断应有充分依据
   - 诊断变更应有合理解释

2. **诊断规范性**
   - 使用标准疾病分类名称
   - 诊断排序符合主次关系
   - 诊断编码准确（如有）

3. **诊断完整性**
   - 主要疾病诊断明确
   - 并发症和合并症记录完整
   - 症状性诊断应有病因学诊断

### 2.3 临床信息质量校验

#### 评估维度
1. **入院症状体征**
   - 描述完整性（主要症状、体征、生命体征）
   - 描述准确性（症状特点、体征特征）
   - 与诊断的相关性

2. **化验结果**
   - 重要异常结果记录完整
   - 结果与诊断的一致性
   - 动态变化趋势记录

3. **特殊检查**
   - 重要检查结果记录完整
   - 检查结论准确性
   - 与诊治过程的相关性

4. **重要会诊**
   - 会诊时机合理性
   - 会诊意见记录完整
   - 会诊建议执行情况

### 2.4 治疗过程校验

#### 评估维度
1. **病程记录**
   - 病情演变过程记录完整
   - 治疗方案调整有据可循
   - 重要医疗决策记录清楚

2. **治疗结果**
   - 治疗效果评估客观
   - 症状改善程度明确
   - 实验室指标变化记录

3. **并发症**
   - 并发症发生时间明确
   - 处理措施记录完整
   - 预后评估合理

4. **治疗效果**
   - 疗效判断标准明确
   - 效果评估客观真实
   - 随访建议合理

### 2.5 出院管理校验

#### 评估维度
1. **出院情况**
   - 出院时症状体征描述完整
   - 生命体征记录完整
   - 精神状态和活动能力描述

2. **出院带药**
   - 药物名称、剂量、用法明确
   - 用药时间长度说明
   - 特殊用药注意事项

3. **出院建议**
   - 生活方式建议具体
   - 复诊时间安排合理
   - 注意事项交代清楚
   - 紧急情况处理指导

4. **随访安排**
   - 随访时间安排合理
   - 随访内容明确
   - 特殊情况处理指导

### 2.6 肿瘤标记一致性校验

#### 检查规则
- 肿瘤标记与诊断内容的一致性
- 肿瘤相关关键词：肿瘤、癌、瘤、白血病、淋巴瘤、肉瘤、恶性、转移、化疗、放疗

#### 校验逻辑
- 标记为肿瘤患者但诊断中无肿瘤相关内容：严重错误
- 诊断中有肿瘤相关内容但未标记：中等错误

### 2.7 跨要素一致性校验

#### 评估维度
1. **诊断与症状一致性**
   - 入院症状与入院诊断相符
   - 出院诊断与治疗过程相符
   - 并发症与病程记录一致

2. **检查与诊断一致性**
   - 化验结果支持诊断
   - 影像学检查与诊断相符
   - 病理结果与诊断一致

3. **治疗与诊断一致性**
   - 治疗方案符合诊断
   - 用药与诊断相匹配
   - 手术指征明确

4. **时间逻辑一致性**
   - 病程发展时间合理
   - 检查时间顺序正确
   - 治疗时机恰当

---

## 3. 问题分类体系

### 3.1 基础信息类问题
| 问题类型 | 代码 | 描述 |
|---------|------|------|
| 缺少必填字段 | missing_required_field | 病区、床位号、门诊号、住院号等必填字段缺失 |

### 3.2 诊断逻辑类问题
| 问题类型 | 代码 | 描述 |
|---------|------|------|
| 诊断演进不合理 | inconsistent_progression | 门诊→入院→出院诊断演进逻辑不合理 |
| 诊断术语不规范 | incorrect_terminology | 使用非标准疾病分类名称 |
| 缺少必要诊断 | missing_diagnosis | 缺少主要疾病或并发症诊断 |
| 诊断排序错误 | incorrect_sequence | 主次诊断排序不符合规范 |
| 诊断描述不完整 | incomplete_description | 诊断表述不够完整详细 |

### 3.3 临床信息类问题
| 问题类型 | 代码 | 描述 |
|---------|------|------|
| 症状体征描述不完整 | incomplete_symptoms | 入院症状体征记录不充分 |
| 缺少重要化验结果 | missing_lab_results | 重要异常化验结果未记录 |
| 检查结果与诊断不符 | inconsistent_findings | 检查结果不支持诊断 |
| 会诊记录不充分 | insufficient_consultation | 重要会诊记录缺失或不完整 |
| 缺少动态变化分析 | missing_trend_analysis | 缺少病情变化趋势记录 |

### 3.4 治疗过程类问题
| 问题类型 | 代码 | 描述 |
|---------|------|------|
| 病程记录不完整 | incomplete_course | 病情演变过程记录不充分 |
| 治疗效果评估不明确 | unclear_treatment_effect | 疗效评估缺乏客观依据 |
| 并发症处理记录缺失 | missing_complication_management | 并发症处理措施记录不完整 |
| 治疗结果评估不客观 | unrealistic_outcome | 治疗结果评估不真实 |
| 随访建议不充分 | insufficient_follow_up | 随访安排不合理 |

### 3.5 出院管理类问题
| 问题类型 | 代码 | 描述 |
|---------|------|------|
| 出院情况描述不完整 | incomplete_discharge_status | 出院时症状体征记录不充分 |
| 出院带药说明不清楚 | unclear_medication | 用药指导不明确 |
| 出院建议不充分 | insufficient_advice | 生活指导和注意事项不完整 |
| 缺少随访安排 | missing_follow_up | 未安排必要的随访 |
| 紧急情况指导不明确 | unclear_emergency_guidance | 紧急情况处理指导不清楚 |

### 3.6 肿瘤标记类问题
| 问题类型 | 代码 | 描述 |
|---------|------|------|
| 肿瘤标记不一致 | tumor_flag_inconsistency | 肿瘤标记与诊断内容不一致 |
| 肿瘤标记缺失 | tumor_flag_missing | 应标记为肿瘤患者但未标记 |

### 3.7 一致性检查类问题
| 问题类型 | 代码 | 描述 |
|---------|------|------|
| 诊断与症状不符 | diagnosis_symptom_mismatch | 诊断与临床症状不匹配 |
| 检查结果与诊断不一致 | test_diagnosis_inconsistency | 检查结果不支持诊断 |
| 治疗与诊断不匹配 | treatment_diagnosis_mismatch | 治疗方案与诊断不符 |
| 时间逻辑不一致 | timeline_inconsistency | 时间序列逻辑错误 |
| 用药与诊断不符 | medication_diagnosis_mismatch | 用药方案与诊断不匹配 |

### 3.8 系统错误类问题
| 问题类型 | 代码 | 描述 |
|---------|------|------|
| 解析错误 | parse_error | LLM响应解析失败 |

---

## 4. 严重程度定义

### 4.1 严重程度分级

| 级别 | 代码 | 描述 | 处理建议 |
|------|------|------|----------|
| 轻微 | low | 建议性修改 | 可选择性修改，不影响整体质量 |
| 中等 | medium | 较强烈建议修改 | 建议修改，影响病历质量 |
| 严重 | high | 必须修改 | 必须修改，严重影响病历质量和安全 |

### 4.2 严重程度判定原则

#### 严重级别 (high)
- 缺少必填基础信息
- 诊断逻辑严重错误
- 治疗与诊断严重不符
- 重要临床信息缺失
- 肿瘤标记严重错误

#### 中等级别 (medium)
- 诊断术语不够规范
- 临床信息不够完整
- 治疗记录不够详细
- 出院指导不够充分

#### 轻微级别 (low)
- 描述不够详细
- 格式不够规范
- 建议性改进

---

## 5. 评分体系

### 5.1 分数区间定义

| 分数区间 | 状态 | 代码 | 描述 |
|----------|------|------|------|
| 90-100分 | 通过 | pass | 质量优良，符合标准 |
| 70-89分 | 警告 | warning | 存在轻微问题，建议改进 |
| 0-69分 | 错误 | error | 存在严重问题，必须修改 |

### 5.2 评分计算方法

1. **单项评分**：每个质控项目独立评分（0-100分）
2. **加权总分**：根据权重计算加权平均分
3. **扣分规则**：
   - 严重问题：扣分较多
   - 中等问题：扣分中等
   - 轻微问题：扣分较少

### 5.3 整体状态判定

最终的整体状态基于加权总分确定：
- 总分 ≥ 90：整体状态为 "pass"
- 70 ≤ 总分 < 90：整体状态为 "warning"
- 总分 < 70：整体状态为 "error"

---

## 6. 输出格式规范

### 6.1 质控结果结构

```json
{
  "overall_score": 85.5,
  "overall_status": "warning",
  "total_deduction": 14.5,
  "elements": [
    {
      "element": "基础信息完整性",
      "score": 100,
      "status": "pass",
      "suggestions": []
    }
  ],
  "cross_check_results": {},
  "suggestions": [
    {
      "type": "问题类型代码",
      "message": "具体问题描述",
      "severity": "严重程度",
      "category": "问题分类",
      "elements": ["相关要素1", "相关要素2"]
    }
  ]
}
```

### 6.2 字段说明

- **overall_score**: 加权总分（0-100）
- **overall_status**: 整体状态（pass/warning/error）
- **total_deduction**: 总扣分
- **elements**: 各质控项目详细结果
- **suggestions**: 所有问题建议汇总

---

## 7. 使用指导

### 7.1 质控流程

1. **输入准备**：确保患者信息和出院病历数据完整
2. **质控执行**：按照既定流程进行七项质控检查
3. **结果分析**：分析质控结果，重点关注严重和中等问题
4. **改进建议**：根据建议进行相应的病历修改

### 7.2 注意事项

1. **并发执行**：LLM质控项目支持并发执行，提高效率
2. **结果解析**：确保LLM响应结果正确解析为JSON格式
3. **异常处理**：处理LLM响应解析失败的情况
4. **权重调整**：可根据实际需求调整各项目权重

### 7.3 质量监控

- 定期评估质控规则的准确性
- 收集临床反馈，优化质控标准
- 监控LLM质控的稳定性和准确性

---

## 8. 版本更新记录

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0 | 2025-01-17 | 初版发布，建立基础质控规范 |

---

## 附录

### A. 肿瘤相关关键词列表
- 肿瘤、癌、瘤、白血病、淋巴瘤、肉瘤
- 恶性、转移、化疗、放疗

### B. 常见问题示例
[此处可添加常见质控问题的具体示例]

### C. 质控模板
[此处可添加标准的质控检查清单模板] 