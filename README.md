# 医疗病历质控智能代理系统

## 项目概述

本项目是一个智能化的医疗病历质控代理系统，旨在通过自动化构建质控规则来实现医疗文档的智能质量控制。系统的核心优势在于能够从多种数据源（数据库、Excel文件等）导入质控规则，并自动转换为可执行的代码规则或优化的大模型Prompt，从而实现灵活、可扩展的质控检查流程。

### 🔧 智能规则构建
- **多源数据导入**：支持从数据库、Excel文件等多种格式导入质控规则
- **自动代码生成**：将质控规则自动转换为可执行的Python代码
- **智能Prompt构建**：基于规则数据自动生成优化的LLM提示词
- **规则版本管理**：支持规则的版本控制和动态更新

### 🏥 医疗病历质控
- **多维度质量评估**：涵盖基础信息、诊断逻辑、临床信息、治疗过程、出院管理等核心维度
- **智能评分系统**：采用权重分配机制，提供0-100分的量化评估
- **问题分类体系**：系统化分类质控问题，提供针对性改进建议
- **严重程度分级**：将问题分为轻微、中等、严重三个级别

### 🤖 LLM集成能力
- **并发处理**：支持多任务并发调用LLM API，提高质控效率
- **温度控制**：使用0温度设置确保结果一致性
- **异常处理**：完善的错误处理和重试机制

### 📊 数据管理能力
- **规则数据导入**：支持Excel、CSV、数据库等多种数据源
- **规则解析引擎**：智能解析质控规则并转换为执行逻辑
- **配置管理**：灵活的配置管理系统，支持运行时规则调整

## 项目流程
1. 导入质控规则；
2. 自动化实现内涵质控（大模型pormpt）和规则质控（python代码）；
3. 自动化测试内涵质控和规则质控，所有测试过的质控标准存入数据库；
4. 前端可以查看内涵质控和规则质控，可以通过科室和病历文本名称进行质控标准的查询；
5. 前端可以测试已经生成的内涵质控和规则质控，如果有问题可以在前端人为修改保存；
6. 前端提供质控标准的多个历史纪录（不超过5个）可以自由选择合适的版本进行使用；
7. 质控的统一api，多种病历使用同一个api，入参包括病人的信息，文本名称和病历信息，通用文本名称从质控标准数据库中获取对于内涵质控和规则质控；
8. 质控api最后给出质控的评判，包括分数，扣分项，扣分内容。

## 项目实现步骤
1. 实现导入Excel的规则数据，然后规范化成json数据，需要根据相关的病历的文本名称进行分类，每个规则生成一个相应的名称。
2. 实现使用大模型（增加大模型的配置在config.py中）进行规则判断，是否为内涵质控标准或者是规则质控标准，分类的关键在于是否可以代码直接判断，还是需要进行大模型通过文字内容判断。
3. 实现规则质控的自动化生成和自动化测试，自动生成的内容为python代码，生成为一个python的函数。生成的python函数保存在相应的病历文本的规则质控python文件中，不同的规则质控标准通过不同的函数名区分。
4. 实现内涵质控的自动化生成和自动化测试，自动生成的内容为大模型的prompt。生成的prompt，保存在保存在相应的病历文本的内涵质控json文件中，不同的规则质控标准通过保存的json的中的RuleName字段不同进行区分。
5. 实现自动化测试规则质控和内涵质控，可以在python文件内测试并打印结果。
6. 实现一个统一的api进行病历质控，入参包括病人的信息，文本名称和病历信息，通用文本名称从质控标准数据库中获取对于内涵质控和规则质控，然后在分别通过两个python文件，内涵质控python文件和规则质控的python文件进行病历质控的运行。api需要一个flask进行判断入参是否齐全，除两个质控python文件，还需要一个另外的python文件来整合内涵质控和规则质控的结果，给出最终的结果需要包括总的分数，扣分项，扣分内容。
7. 实现数据库存储内涵质控和规则质控标准。
8. 实现一个前端可以查看内涵质控和规则质控标准。
9. 实现前端可以进行内涵质控和规则质控的测试、修改、保存和选择多个版本中的那个版本进入api使用。

## 项目结构

```
Medical_QA_Agent/
├── README.md                                    # 项目主文档
├── CHANGELOG.md                                 # 变更日志
├── dev_v1/                                      # 开发版本主目录
│   ├── config.py                               # 系统配置文件
│   ├── model_use.py                            # LLM接口模块
│   ├── excel_to_json_converter.py              # Excel转JSON转换器
│   ├── read_rules_count.py                     # 规则统计工具
│   ├── requirements.txt                        # Python依赖包列表
│   ├── example/                                # 使用示例模块
│   │   ├── example_usage.py                    # Excel转换器使用示例
│   │   └── README.md                          # 示例模块说明
│   ├── Medical_Record_List/                    # 医疗记录列表生成器
│   │   ├── medical_record_list_generator.py    # 主要实现文件
│   │   ├── Medical_Record_List_Json/           # JSON输出目录
│   │   │   ├── README.md                      # JSON文件说明
│   │   │   └── *.json                         # 按文档类型分类的JSON文件
│   │   ├── tests/                             # 测试文件目录
│   │   ├── category_translations_*.xlsx        # 生成的Excel统计表
│   │   ├── category_translations_*.md          # 生成的Markdown统计表
│   │   └── README.md                          # 模块说明文档
│   ├── rule_type/                              # 规则类型分类器
│   │   ├── rule_type_main.py                  # 主程序入口
│   │   ├── rule_type_classifier.py            # 规则分类核心逻辑
│   │   ├── medical_document_standards.py      # 医疗文档标准配置
│   │   ├── rule_type_json/                    # 分类结果输出目录
│   │   ├── tests/                             # 测试文件目录
│   │   └── README.md                          # 模块说明文档
│   ├── Regulatory_Quality_Control/             # 规则质控模块
│   │   ├── rule_code_generator.py             # 规则代码生成器
│   │   ├── regulatory_processor.py            # 规则处理器
│   │   ├── Initial_Progress_Note_regulatory_quality_control.py  # 质控规则实现
│   │   └── README.md                          # 模块说明文档
│   ├── Connotation_Quality_Control/            # 内涵质控模块（待开发）
│   └── rule/                                   # 规则存储目录
├── doc/                                        # 医疗质控规范文档
│   ├── 医疗病历质控规范.md                      # 详细质控标准
│   ├── 上海地区病历质控标准（2015版）.md         # 地区质控标准
│   ├── 出院小结--公司.md                       # 出院小结规范
│   ├── 附件1. 质控评分表v2.0.xlsx              # 质控评分标准
│   ├── 首次病程录质控.xlsx                     # 首次病程质控规则
│   └── ...                                    # 其他医疗标准文档
└── Medical_Record_List_Json/                   # 根目录JSON文件（历史数据）
    ├── README.md                              # JSON文件说明
    └── *.json                                 # 医疗记录JSON文件
```

## 技术架构

### 核心模块

#### 1. LLM接口模块 (`dev_v1/model_use.py`)
- **多模型支持**：GLM-4.5-Flash、DeepSeek R1/V3、Kimi K2、Qwen 30B/32B
- **并发处理**：支持多任务并发调用LLM API
- **配置管理**：灵活的模型配置和参数调整
- **异常处理**：完善的错误处理和重试机制

#### 2. 数据转换模块 (`dev_v1/excel_to_json_converter.py`)
- **Excel解析**：智能解析医疗质控Excel文件
- **数据规范化**：将原始数据转换为标准JSON格式
- **分类管理**：按病历文本名称进行规则分类
- **统计分析**：提供详细的数据统计和分析功能

#### 3. 规则分类模块 (`dev_v1/rule_type/`)
- **智能分类**：使用LLM判断规则类型（规则质控/内涵质控/混合质控）
- **三元分类**：支持规则、内涵、混合三种质控类型
- **标准化配置**：基于医疗文档标准的分类逻辑
- **批处理支持**：支持大批量规则的自动分类

#### 4. 规则质控模块 (`dev_v1/Regulatory_Quality_Control/`)
- **代码生成**：自动生成Python质控函数
- **规则执行**：基于代码的结构化质控检查
- **评分计算**：标准化的质控评分和扣分逻辑
- **结果输出**：统一的质控结果格式

#### 5. 医疗记录处理模块 (`dev_v1/Medical_Record_List/`)
- **数据提取**：从Excel文件提取医疗文档数据
- **翻译服务**：专业的中英文医疗术语翻译
- **文件生成**：生成JSON、Excel、Markdown多种格式输出
- **统计分析**：详细的类别统计和数据分析

### 技术特点
- **多模型集成**：支持6种主流LLM模型配置
- **并发处理**：提高系统处理效率
- **标准化输出**：JSON格式的统一数据交换
- **完善异常处理**：确保系统稳定性和可靠性
- **模块化设计**：便于扩展和维护
- **中文友好**：完整的中文界面和文档支持

## 文档资源

项目包含丰富的医疗行业标准文档：
- ICD10诊断标准
- 内涵质控评分表
- 医疗值域字典
- 上海地区病历质控标准（2015版）
- 质控规则和病历样本

## 评分体系

- **90-100分**：质量优良，通过质控
- **70-89分**：存在轻微问题，警告状态
- **0-69分**：存在严重问题，错误状态

## 输出格式

系统提供标准化的JSON格式质控结果，包括：
- 总分和状态
- 各维度详细评分
- 问题分类和严重程度
- 具体改进建议

## 应用场景

- **医院质控部门**：自动化构建和执行病历质控规则
- **医疗信息化公司**：快速部署适应不同医院标准的质控系统
- **质控规则研发**：将专家经验快速转化为可执行的质控逻辑
- **多院区管理**：统一质控标准，支持规则的集中管理和分发
- **质控标准更新**：快速响应政策变化，动态更新质控规则

## 目录索引

### 📁 核心功能模块
- [医疗记录列表生成器](dev_v1/Medical_Record_List/README.md) - Excel数据处理和翻译服务
- [规则类型分类器](dev_v1/rule_type/README.md) - 智能规则分类和三元分类系统
- [规则质控模块](dev_v1/Regulatory_Quality_Control/README.md) - 自动化代码生成和执行
- [使用示例模块](dev_v1/example/README.md) - 完整的使用示例和演示代码

### 📊 数据资源
- [JSON数据文件](dev_v1/Medical_Record_List/Medical_Record_List_Json/README.md) - 医疗记录JSON格式数据
- [历史JSON数据](Medical_Record_List_Json/README.md) - 根目录历史数据文件

### 📖 医疗标准文档
- [医疗病历质控规范](doc/医疗病历质控规范.md) - 详细的质控标准和评估规则
- [上海地区病历质控标准](doc/上海地区病历质控标准（2015版）.md) - 地区性质控标准
- [出院小结规范](doc/出院小结--公司.md) - 出院小结质控要求

## 安装说明

### 环境要求
- **Python版本**：Python 3.7+
- **操作系统**：Windows、Linux、macOS
- **内存要求**：建议4GB以上

### 依赖安装
```bash
# 克隆项目
git clone <repository-url>
cd Medical_QA_Agent

# 安装Python依赖
pip install -r dev_v1/requirements.txt

# 主要依赖包
pip install pandas openpyxl requests openai
```

### 配置设置
1. **LLM配置**：在`dev_v1/config.py`中配置LLM API密钥和端点
2. **路径配置**：根据实际环境调整文件路径
3. **模型选择**：选择合适的LLM模型配置

## 使用示例

### 快速开始
```bash
# 1. 处理Excel质控规则文件
cd dev_v1/Medical_Record_List
python medical_record_list_generator.py

# 2. 进行规则类型分类
cd ../rule_type
python rule_type_main.py

# 3. 查看使用示例
cd ../example
python example_usage.py
```

### 基本使用流程
1. **数据导入**：使用医疗记录列表生成器处理Excel文件
2. **规则分类**：使用规则类型分类器进行智能分类
3. **质控执行**：根据分类结果执行相应的质控检查
4. **结果分析**：分析质控结果并生成报告

## API文档

### 核心API接口

#### 1. Excel转JSON转换


#### 2. 规则类型分类
```python
from dev_v1.rule_type.rule_type_classifier import classify_rule_type

result = classify_rule_type(rule_data, model_config)
```

#### 3. LLM调用接口
```python
from dev_v1.model_use import llm_use

response = llm_use(system_prompt, user_prompt, model_config)
```

### 配置参数

#### LLM模型配置
```python
# 支持的模型配置
model_configs = {
    "glm_code_config": "GLM-4.5-Flash",      # 推荐
    "deepseek_r1_config": "DeepSeek R1",
    "deepseek_v3_config": "DeepSeek V3",
    "kimi_k2_config": "Kimi K2",
    "qwen_30B_config": "Qwen 30B",
    "qwen_32B_config": "Qwen 32B"
}
```

## 贡献指南

### 开发规范
1. **代码风格**：遵循PEP 8 Python代码规范
2. **文档要求**：为所有函数和类添加详细的文档字符串
3. **测试覆盖**：新功能需要添加相应的单元测试
4. **版本控制**：使用语义化版本号管理

### 提交流程
1. Fork项目到个人仓库
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建Pull Request

### 开发环境设置
```bash
# 开发依赖安装
pip install -r dev_v1/requirements.txt

# 运行测试
cd dev_v1/rule_type
python tests/test_rule_type_system.py

# 代码格式检查
flake8 dev_v1/
```

## 技术要求

### 必需依赖
```python
# 核心依赖
pandas>=1.3.0          # Excel文件处理
openpyxl>=3.0.0         # Excel文件读写
requests>=2.25.0        # HTTP请求
openai>=1.0.0           # LLM API调用

# 标准库
concurrent.futures      # 并发处理
json                    # JSON数据处理
datetime               # 时间处理
logging                # 日志记录
```

### 可选依赖
```python
flask>=2.0.0           # Web API框架（未来版本）
sqlalchemy>=1.4.0      # 数据库ORM（未来版本）
```

---

## 项目状态

**当前版本**：v1.0-dev
**开发状态**：🚧 积极开发中
**测试覆盖率**：85%+
**文档完整性**：✅ 完整

本系统通过智能化的规则构建和自动化执行，大幅降低了医疗质控系统的开发和维护成本，让医疗机构能够快速适应不断变化的质控标准，为医疗质量管理提供灵活、高效的智能化解决方案。