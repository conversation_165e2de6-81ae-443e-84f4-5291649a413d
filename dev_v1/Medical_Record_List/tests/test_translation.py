# -*- coding: utf-8 -*-
"""
测试翻译功能 - 只翻译少量示例
"""

import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/..")

from medical_record_list_generator import MedicalRecordListGenerator
from config import deepseek_v3_config

def test_translation_functionality():
    """测试翻译功能"""
    print("开始测试翻译功能...")
    
    # 设置参数
    excel_path = "../../../doc/附件1. 质控评分表v2.0.xlsx"
    model_config = deepseek_v3_config
    output_dir = "."  # 使用当前目录

    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")
    
    # 创建生成器实例
    generator = MedicalRecordListGenerator()
    
    # 执行前3步
    print("\n=== 执行基础步骤 ===")
    if not generator.read_excel_file(excel_path):
        return False
    if not generator.validate_and_extract_data():
        return False
    if not generator.generate_category_statistics():
        return False
    
    # 创建小规模翻译测试数据
    print("\n=== 创建小规模翻译测试 ===")
    limited_translations = {}
    
    for category, stats in generator.category_stats.items():
        # 只取前3个值进行翻译测试
        test_values = stats['unique_values'][:3]
        print(f"\n测试翻译 {category} 的前3个值: {test_values}")
        
        category_translations = {}
        for chinese_term in test_values:
            try:
                system_prompt = """你是一个专业的医疗术语翻译专家。请将中文医疗术语准确翻译成英文。
要求：
1. 提供准确的医疗专业术语翻译
2. 只返回英文翻译结果，不要包含其他内容
3. 如果是专有名词，保持专业性和准确性
4. 翻译应该简洁明了
5. 使用标准的医疗英语术语"""
                
                user_prompt = f"请将以下中文医疗术语翻译成英文，只返回英文翻译：{chinese_term}"
                
                # 调用LLM进行翻译
                from model_use import llm_use
                english_translation = llm_use(system_prompt, user_prompt, model_config)
                
                if english_translation:
                    english_translation = english_translation.strip().strip('"').strip("'")
                    category_translations[chinese_term] = english_translation
                    print(f"  {chinese_term} -> {english_translation}")
                else:
                    category_translations[chinese_term] = chinese_term
                    print(f"  {chinese_term} -> [翻译失败，使用原文]")
                    
            except Exception as e:
                print(f"  翻译 '{chinese_term}' 时出错: {e}")
                category_translations[chinese_term] = chinese_term
        
        limited_translations[category] = category_translations
    
    # 设置翻译结果
    generator.translations = limited_translations
    
    # 测试Excel和Markdown生成
    print("\n=== 测试Excel统计表生成 ===")
    if generator.generate_excel_statistics(output_dir):
        print("Excel统计表生成成功")
    else:
        print("Excel统计表生成失败")
    
    print("\n=== 测试Markdown统计表生成 ===")
    if generator.generate_markdown_statistics(output_dir):
        print("Markdown统计表生成成功")
    else:
        print("Markdown统计表生成失败")
    
    # 显示生成的文件
    print(f"\n=== 检查生成的文件 ===")
    if os.path.exists(output_dir):
        files = os.listdir(output_dir)
        print(f"输出目录 {output_dir} 中的文件:")
        for file in files:
            file_path = os.path.join(output_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"  - {file} ({file_size} bytes)")
    else:
        print(f"输出目录 {output_dir} 不存在")
    
    print("\n翻译功能测试完成！")
    return True

if __name__ == "__main__":
    test_translation_functionality()
