# -*- coding: utf-8 -*-
import json
import os
import sys
import logging
from datetime import datetime

# 添加当前目录到路径以导入rule_type_classifier
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rule_type_classifier import classify_rule_type

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def process_rules_file(input_file_path, output_dir=None):
    """
    读取规则JSON文件，对每个规则进行分类，并保存到新的JSON文件中
    
    Args:
        input_file_path (str): 输入JSON文件路径
        output_dir (str): 输出目录，默认为输入文件所在目录
        
    Returns:
        str: 输出文件路径
    """
    
    logger.info(f"开始处理规则文件: {input_file_path}")
    
    try:
        # 读取输入文件
        with open(input_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        logger.info(f"成功读取文件，包含 {len(data.get('rules', []))} 条规则")
        
        # 处理每个规则
        processed_rules = []
        for rule in data.get('rules', []):
            logger.info(f"正在处理规则: {rule.get('rule_name', '')}")
            
            # 调用分类函数
            rule_type = classify_rule_type(rule)
            
            # 添加type字段到规则中
            rule_with_type = rule.copy()
            rule_with_type['type'] = rule_type
            
            processed_rules.append(rule_with_type)
            
        # 更新数据结构
        processed_data = data.copy()
        processed_data['rules'] = processed_rules
        
        # 更新元数据
        if 'metadata' in processed_data:
            processed_data['metadata']['processing_time'] = datetime.now().isoformat()
            processed_data['metadata']['type_classification_added'] = True
        
        # 确定输出文件路径
        if output_dir is None:
            output_dir = os.path.dirname(input_file_path)
            
        # 从输入文件名生成输出文件名
        input_filename = os.path.basename(input_file_path)
        document_type = data.get('rules', [{}])[0].get('document_type', 'Unknown')
        output_filename = f"{document_type}_type.json"
        output_file_path = os.path.join(output_dir, output_filename)
        
        # 保存处理后的数据
        with open(output_file_path, 'w', encoding='utf-8') as f:
            json.dump(processed_data, f, ensure_ascii=False, indent=2)
            
        logger.info(f"处理完成，结果已保存到: {output_file_path}")
        
        # 统计分类结果
        type_counts = {}
        for rule in processed_rules:
            rule_type = rule.get('type', 'Unknown')
            type_counts[rule_type] = type_counts.get(rule_type, 0) + 1
            
        logger.info(f"分类统计: {type_counts}")
        
        return output_file_path
        
    except FileNotFoundError:
        logger.error(f"文件未找到: {input_file_path}")
        raise
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误: {e}")
        raise
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        raise

def main():
    """
    主函数，处理Initial_Progress_Note.json文件
    """
    
    # 输入文件路径
    input_file = "d:\\Documents\\Program\\CCOS\\LLMs2\\Hospital\\Medical_QA_Agent\\dev_v1\\rule\\Initial_Progress_Note.json"
    
    # 输出目录（与输入文件同目录）
    output_dir = os.path.dirname(input_file)
    
    try:
        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            logger.error(f"输入文件不存在: {input_file}")
            return
            
        # 处理文件
        output_file = process_rules_file(input_file, output_dir)
        
        print(f"\n处理完成！")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        
    except Exception as e:
        logger.error(f"主程序执行失败: {e}")
        print(f"处理失败: {e}")

if __name__ == '__main__':
    main()