"""
医疗文档标准配置
定义各类医疗文档的标准章节要求和内容要素
"""

# 文档类型标准化映射表
DOCUMENT_TYPE_ALIASES = {
    "首次病程": "首次病程记录",
    "首次病程记录": "首次病程记录",
    "入院记录": "入院记录", 
    "入院病历": "入院记录",
    "出院记录": "出院记录",
    "出院小结": "出院记录",
    "病程记录": "病程记录",
    "日常病程记录": "日常病程记录",
    "术后首次病程记录": "术后首次病程记录",
    "主治医师首次查房记录": "主治医师首次查房记录",
    "（副）主任医师首次查房记录": "主任医师首次查房记录"
}

# 各文档类型的必需章节清单
REQUIRED_SECTIONS = {
    "首次病程记录": [
        "病例特点",
        "初步诊断", 
        "诊断依据",
        "鉴别诊断",
        "诊疗计划"
    ],
    
    "入院记录": [
        "主诉",
        "现病史",
        "既往史",
        "个人史",
        "家族史",
        "体格检查",
        "辅助检查",
        "初步诊断"
    ],
    
    "出院记录": [
        "入院情况",
        "入院诊断",
        "诊疗经过",
        "出院诊断",
        "出院情况",
        "出院医嘱"
    ],
    
    "日常病程记录": [
        "主观资料",
        "客观资料", 
        "评估",
        "计划"
    ],
    
    "术后首次病程记录": [
        "手术经过",
        "术后诊断",
        "术后医嘱",
        "术后注意事项"
    ]
}

# 各章节的必需内容要素
SECTION_CONTENT_REQUIREMENTS = {
    "病例特点": [
        "主要临床表现",
        "重要阳性体征",
        "关键辅助检查结果",
        "病情特点分析"
    ],
    
    "现病史": [
        "发病时间",
        "主要症状特点",
        "症状发展变化过程",
        "伴随症状",
        "诊疗经过及结果",
        "一般情况变化"
    ],
    
    "体格检查": [
        "一般情况",
        "生命体征",
        "系统查体",
        "专科检查"
    ],
    
    "诊疗计划": [
        "进一步检查计划",
        "治疗方案",
        "护理措施",
        "病情观察要点"
    ]
}

# 规则质控关键词识别（结构性问题）
RULE_BASED_KEYWORDS = [
    # 时效性相关
    "未在.*小时内完成",
    "超时",
    "延迟",
    "逾期",

    # 整个章节缺失相关（精确匹配）
    "^缺[主现既个家体辅初入出诊病]",  # 缺主诉、缺现病史等
    "^无.*记录$",
    "^未记录.*$",

    # 签名格式相关
    "签名",
    "未签名",
    "签字",
    "盖章",

    # 格式规范相关
    "格式",
    "规范",
    "标准",
    "模板"
]

# 内涵质控关键词识别（内容质量问题）
CONTENT_BASED_KEYWORDS = [
    # 内容充分性相关（章节存在但内容不足）
    "描述.*缺陷",
    "描述.*有缺陷",
    "内容.*不充分",
    "记录.*不详细",
    ".*缺.*情况$",  # 现病史缺发病情况
    ".*缺.*特点$",  # 缺主要症状特点
    ".*缺.*依据$",  # 缺诊断依据
    ".*缺.*过程$",  # 缺发展变化过程
    ".*缺.*症状$",  # 缺伴随症状
    ".*缺.*经过$",  # 缺诊疗经过

    # 医疗质量相关
    "诊断.*不准确",
    "治疗.*不合理",
    "用药.*不当",
    "检查.*不必要",

    # 逻辑一致性相关
    "不符",
    "矛盾",
    "不一致",
    "冲突"
]

# 明确的章节缺失模式（规则质控）
SECTION_MISSING_PATTERNS = [
    r"^缺主诉$",
    r"^缺现病史$",
    r"^缺既往史$",
    r"^缺个人史$",
    r"^缺家族史$",
    r"^缺体格检查$",
    r"^缺辅助检查$",
    r"^缺初步诊断$",
    r"^缺入院诊断$",
    r"^缺出院诊断$",
    r"^缺病例特点$",
    r"^缺诊断依据$",
    r"^缺鉴别诊断$",
    r"^缺诊疗计划$",
    r"^缺.*记录$",
    r"^无.*记录$",
    # 特殊格式：文档类型+缺+章节名
    r".*缺病例特点$",
    r".*缺主诉$",
    r".*缺现病史$",
    r".*缺既往史$",
    r".*缺体格检查$",
    r".*缺初步诊断$",
    r".*缺入院诊断$",
    r".*缺出院诊断$",
    r".*缺诊断依据$",
    r".*缺鉴别诊断$",
    r".*缺诊疗计划$",
    # 带条件限制的章节缺失（仍然是结构性问题）
    r".*缺鉴别诊断.*可不判断",
    r".*缺.*诊断.*可不判断",
    r".*缺.*，.*可不.*",
    r".*缺.*，.*不.*判断",
    r".*缺.*，.*待查.*可不判断"
]

# 内容不充分模式（内涵质控）
CONTENT_INSUFFICIENT_PATTERNS = [
    r".*描述.*缺陷",
    r".*缺.*情况$",
    r".*缺.*特点$",
    r".*缺.*依据$",
    r".*缺.*过程$",
    r".*缺.*症状$",
    r".*缺.*经过$",
    r".*缺.*变化$",
    r".*不充分$",
    r".*不详细$",
    r".*不完整$"
]

# 混合型规则识别模式（同时包含规则质控和内涵质控要素）
MIXED_TYPE_PATTERNS = [
    # 章节缺失 + 具体内容要求
    r"缺.*，.*未写明.*",
    r"缺.*，.*未.*明.*",
    r"缺.*，.*缺.*等$",
    r"缺.*，.*不.*等$",
    r"缺.*医嘱.*未写明.*",
    r"缺.*记录.*未.*明.*",
    r"缺.*记录.*未详细.*",
    r"缺.*记录.*描述.*详细",
    r"缺.*记录.*过程.*详细",
    # 特殊的"缺XX记录，XX过程..."模式
    r"缺.*记录，.*过程.*记录.*",
    r"缺.*记录，.*过程.*详细.*",
    r"缺.*记录，.*未详细记录.*",
    r"缺.*记录，.*描述.*详细",
    # 其他混合模式
    r"缺.*，.*要求.*交待",
    r"缺.*，.*注意事项.*",
    r"缺.*，.*随访.*要求.*",
    r"缺.*，.*具体.*方案",
    r"缺.*，.*指导.*",
    r"缺.*，.*安排.*",
    r"缺.*，.*步骤.*",
    r"缺.*，.*信息.*",
    r"缺.*，.*关键信息",
    # 特殊的混合表述
    r"缺.*，.*分析.*充分",
    r"缺.*，.*表现.*分析",
    r"缺.*，.*临床.*分析",
    # 复杂的混合模式
    r"缺.*，.*出血量.*",
    r"缺.*，.*麻醉.*",
    r"缺.*，.*药名.*剂量.*",
    r"缺.*，.*带药.*总量.*"
]

def normalize_document_type(doc_type: str) -> str:
    """
    标准化文档类型名称
    
    Args:
        doc_type: 原始文档类型名称
        
    Returns:
        str: 标准化后的文档类型名称
    """
    return DOCUMENT_TYPE_ALIASES.get(doc_type, doc_type)

def get_required_sections(doc_type: str) -> list:
    """
    获取指定文档类型的必需章节列表
    
    Args:
        doc_type: 文档类型
        
    Returns:
        list: 必需章节列表
    """
    normalized_type = normalize_document_type(doc_type)
    return REQUIRED_SECTIONS.get(normalized_type, [])

def get_section_content_requirements(section_name: str) -> list:
    """
    获取指定章节的内容要求
    
    Args:
        section_name: 章节名称
        
    Returns:
        list: 内容要求列表
    """
    return SECTION_CONTENT_REQUIREMENTS.get(section_name, [])

def is_structural_issue(rule_content: str, rule_type: str) -> bool:
    """
    判断是否为结构性问题（规则质控）

    Args:
        rule_content: 规则内容
        rule_type: 规则类型

    Returns:
        bool: True表示结构性问题，False表示内容性问题
    """
    import re

    # 时效性问题直接归类为规则质控
    if rule_type == "时效性":
        return True

    # 签名相关问题直接归类为规则质控
    if "签名" in rule_content:
        return True

    # 特殊处理：带条件限制的章节缺失仍然是结构性问题
    # 例如："首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断"
    conditional_section_missing_patterns = [
        r"缺鉴别诊断.*可不判断",
        r"缺.*诊断.*可不判断",
        r"缺[^，]*，.*可不.*",
        r"缺[^，]*，.*不.*判断"
    ]

    for pattern in conditional_section_missing_patterns:
        if re.search(pattern, rule_content):
            return True

    # 检查是否以"缺"开头（包括混合型规则的结构性部分）
    if re.search(r"^缺[^，]*", rule_content):
        return True

    # 检查逗号分隔的第一部分是否为章节缺失
    if "，" in rule_content:
        first_part = rule_content.split("，")[0].strip()
        if re.search(r"^缺", first_part) or re.search(r"^无.*记录", first_part):
            return True

    # 优先检查明确的章节缺失模式
    for pattern in SECTION_MISSING_PATTERNS:
        if re.search(pattern, rule_content):
            return True

    # 检查规则质控关键词
    for keyword in RULE_BASED_KEYWORDS:
        if re.search(keyword, rule_content):
            return True

    return False

def is_content_issue(rule_content: str, rule_type: str) -> bool:
    """
    判断是否为内容性问题（内涵质控）

    Args:
        rule_content: 规则内容
        rule_type: 规则类型

    Returns:
        bool: True表示内容性问题，False表示结构性问题
    """
    import re

    # 特殊处理：如果是混合型，需要单独检查内容性要素
    if is_mixed_type_issue(rule_content, rule_type):
        # 对于混合型规则，检查是否包含内容性要素
        content_indicators = [
            r"未写明.*",         # 未写明XX
            r"未.*明.*",         # 未XX明XX
            r"未详细.*",         # 未详细XX
            r"描述.*详细",       # 描述不详细
            r"记录.*详细",       # 记录不详细
            r"过程.*详细",       # 过程不详细
            r"不.*等$",          # 不XX等
            r"缺.*等$",          # 缺XX等（在混合型中指内容缺失）
            r"要求.*交待",       # 要求XX交待
            r"注意事项",         # 注意事项
            r"随访.*要求",       # 随访要求
            r"具体.*方案",       # 具体治疗方案
            r".*指导",           # XX指导
            r".*安排",           # XX安排
            r".*步骤",           # XX步骤
            r"关键信息",         # 关键信息
            r".*分析.*充分",     # XX分析不充分
            r".*表现.*分析"      # XX表现分析
        ]

        for pattern in content_indicators:
            if re.search(pattern, rule_content):
                return True

    # 如果已经被判定为纯结构性问题，则不是内容性问题
    elif is_structural_issue(rule_content, rule_type):
        return False

    # 检查内容不充分模式
    for pattern in CONTENT_INSUFFICIENT_PATTERNS:
        if re.search(pattern, rule_content):
            return True

    # 检查内涵质控关键词
    for keyword in CONTENT_BASED_KEYWORDS:
        if re.search(keyword, rule_content):
            return True

    return False

def is_mixed_type_issue(rule_content: str, rule_type: str) -> bool:
    """
    判断是否为混合型问题（同时包含规则质控和内涵质控要素）

    Args:
        rule_content: 规则内容
        rule_type: 规则类型

    Returns:
        bool: True表示混合型问题，False表示单一类型问题
    """
    import re

    # 优先检查是否匹配混合型模式
    for pattern in MIXED_TYPE_PATTERNS:
        if re.search(pattern, rule_content):
            return True

    # 更精确的混合型判断：同时包含结构性和内容性要素
    has_structural = False
    has_content = False

    # 检查是否包含结构性要素（章节缺失）
    structural_indicators = [
        r"缺[^，]*[，。]",  # 缺XX，
        r"无.*记录[，。]",   # 无XX记录，
        r"未记录.*[，。]",   # 未记录XX，
        r"^缺[^，]*，",      # 以"缺XX，"开头
        r"^无[^，]*，",      # 以"无XX，"开头
    ]
    for pattern in structural_indicators:
        if re.search(pattern, rule_content):
            has_structural = True
            break

    # 检查是否包含内容性要素（具体内容要求）
    content_indicators = [
        r"未写明.*",         # 未写明XX
        r"未.*明.*",         # 未XX明XX
        r"未详细.*",         # 未详细XX
        r"描述.*详细",       # 描述不详细
        r"记录.*详细",       # 记录不详细
        r"过程.*详细",       # 过程不详细
        r"不.*等$",          # 不XX等
        r"缺.*等$",          # 缺XX等
        r"要求.*交待",       # 要求XX交待
        r"注意事项",         # 注意事项
        r"随访.*要求",       # 随访要求
        r"具体.*方案",       # 具体治疗方案
        r".*指导",           # XX指导
        r".*安排",           # XX安排
        r".*步骤",           # XX步骤
        r"关键信息",         # 关键信息
        r".*分析.*充分",     # XX分析不充分
        r".*表现.*分析"      # XX表现分析
    ]
    for pattern in content_indicators:
        if re.search(pattern, rule_content):
            has_content = True
            break

    # 特殊处理：如果包含逗号分隔的两部分，进一步分析
    if "，" in rule_content:
        parts = rule_content.split("，", 1)
        if len(parts) == 2:
            first_part = parts[0].strip()
            second_part = parts[1].strip()

            # 第一部分是否为章节缺失
            first_structural = any(re.search(p, first_part) for p in [r"^缺", r"^无.*记录", r"^未记录"])

            # 第二部分是否为内容要求
            second_content = any(re.search(p, second_part) for p in content_indicators)

            if first_structural and second_content:
                return True

    return has_structural and has_content

def classify_rule_by_standards(rule_data: dict) -> str:
    """
    基于标准配置进行规则分类（支持三元分类）

    Args:
        rule_data: 规则数据字典

    Returns:
        str: "规则"、"内涵" 或 "规则和内涵"
    """
    rule_content = rule_data.get('rule_content', '')
    rule_type = rule_data.get('rule_type_chinese', '')
    doc_type = rule_data.get('document_type_chinese', '')

    # 标准化文档类型
    normalized_doc_type = normalize_document_type(doc_type)

    # 第一优先级：检查混合型问题（规则和内涵）
    if is_mixed_type_issue(rule_content, rule_type):
        return "规则和内涵"

    # 第二优先级：检查结构性问题（规则质控）
    if is_structural_issue(rule_content, rule_type):
        return "规则"

    # 第三优先级：检查内容性问题（内涵质控）
    if is_content_issue(rule_content, rule_type):
        return "内涵"

    # 第四优先级：基于规则类型的分类
    if rule_type == "时效性":
        return "规则"
    elif rule_type == "段落完整性":
        # 段落完整性需要进一步区分：整个段落缺失vs段落内容不足
        import re
        if re.search(r"^缺[^，。]*$", rule_content):  # 简单的"缺XX"格式
            return "规则"
        else:
            return "内涵"
    elif rule_type == "签名合理性":
        return "规则"
    elif rule_type in ["内容完整性", "数据一致性", "术语规范性"]:
        return "内涵"
    else:
        return "内涵"  # 默认归类为内涵质控
