#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据分类测试脚本
使用实际的医疗记录JSON文件测试新的分类逻辑
"""

import sys
import os
import json
from typing import Dict, List

# 添加父目录到路径以导入核心模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from rule_type_classifier import classify_rule_type, _get_default_classification
from medical_document_standards import classify_rule_by_standards

def load_medical_record_data(file_path: str) -> Dict:
    """加载医疗记录JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 无法加载文件 {file_path}: {e}")
        return {}

def test_classification_on_real_data():
    """测试真实数据的分类效果"""
    print("🔬 真实医疗记录数据分类测试")
    print("=" * 60)
    
    # 测试文件列表
    base_path = os.path.join(current_dir, "..", "Medical_Record_List", "Medical_Record_List_Json")
    test_files = [
        os.path.join(base_path, "Initial_Progress_Note.json"),
        os.path.join(base_path, "Admission_Record.json"),
        os.path.join(base_path, "Discharge_Summary.json")
    ]
    
    total_rules = 0
    classification_stats = {"规则": 0, "内涵": 0, "规则和内涵": 0}
    detailed_results = []
    
    for file_path in test_files:
        print(f"\n📋 测试文件: {os.path.basename(file_path)}")
        print("-" * 40)
        
        data = load_medical_record_data(file_path)
        if not data:
            continue
            
        records = data.get('records', [])
        doc_type = data.get('metadata', {}).get('document_type_chinese', '未知')
        
        print(f"文档类型: {doc_type}")
        print(f"规则总数: {len(records)}")
        
        file_stats = {"规则": 0, "内涵": 0, "规则和内涵": 0}
        
        # 分析前10条规则作为示例
        sample_size = min(10, len(records))
        print(f"\n分析前 {sample_size} 条规则:")
        
        for i, record in enumerate(records[:sample_size]):
            rule_content = record.get('rule_content', '')
            rule_type = record.get('rule_type_chinese', '')
            
            # 使用标准化分类
            classification = classify_rule_by_standards(record)
            
            file_stats[classification] += 1
            classification_stats[classification] += 1
            total_rules += 1
            
            # 记录详细结果
            result = {
                "file": os.path.basename(file_path),
                "rule_content": rule_content,
                "rule_type": rule_type,
                "classification": classification,
                "doc_type": doc_type
            }
            detailed_results.append(result)
            
            # 显示分类结果
            status_icon = "🔧" if classification == "规则" else "📋" if classification == "内涵" else "⚖️"
            print(f"  {i+1:2d}. {status_icon} [{classification}] {rule_type}: {rule_content[:50]}...")
        
        print(f"\n本文件分类统计:")
        print(f"  规则质控: {file_stats['规则']} 条")
        print(f"  内涵质控: {file_stats['内涵']} 条")
        print(f"  混合质控: {file_stats['规则和内涵']} 条")
        
        # 如果有更多规则，统计全部
        if len(records) > sample_size:
            print(f"\n统计全部 {len(records)} 条规则...")
            for record in records[sample_size:]:
                classification = classify_rule_by_standards(record)
                file_stats[classification] += 1
                classification_stats[classification] += 1
                total_rules += 1
            
            print(f"全文件分类统计:")
            print(f"  规则质控: {file_stats['规则']} 条")
            print(f"  内涵质控: {file_stats['内涵']} 条")
            print(f"  混合质控: {file_stats['规则和内涵']} 条")
    
    # 总体统计
    print(f"\n" + "=" * 60)
    print("📊 总体分类统计")
    print("=" * 60)
    print(f"总规则数: {total_rules}")
    if total_rules > 0:
        print(f"规则质控: {classification_stats['规则']} 条 ({classification_stats['规则']/total_rules*100:.1f}%)")
        print(f"内涵质控: {classification_stats['内涵']} 条 ({classification_stats['内涵']/total_rules*100:.1f}%)")
        print(f"混合质控: {classification_stats['规则和内涵']} 条 ({classification_stats['规则和内涵']/total_rules*100:.1f}%)")
    else:
        print("没有找到有效的规则数据")
    
    return detailed_results, classification_stats

def analyze_classification_patterns(results: List[Dict]):
    """分析分类模式"""
    print(f"\n" + "=" * 60)
    print("🔍 分类模式分析")
    print("=" * 60)
    
    # 按规则类型统计
    rule_type_stats = {}
    for result in results:
        rule_type = result['rule_type']
        classification = result['classification']
        
        if rule_type not in rule_type_stats:
            rule_type_stats[rule_type] = {"规则": 0, "内涵": 0}
        
        rule_type_stats[rule_type][classification] += 1
    
    print("按规则类型分类统计:")
    for rule_type, stats in rule_type_stats.items():
        total = stats['规则'] + stats['内涵']
        rule_pct = stats['规则'] / total * 100 if total > 0 else 0
        content_pct = stats['内涵'] / total * 100 if total > 0 else 0
        
        print(f"  {rule_type}:")
        print(f"    规则质控: {stats['规则']} 条 ({rule_pct:.1f}%)")
        print(f"    内涵质控: {stats['内涵']} 条 ({content_pct:.1f}%)")
    
    # 典型案例展示
    print(f"\n典型分类案例:")
    
    # 规则质控案例
    rule_cases = [r for r in results if r['classification'] == '规则'][:3]
    print(f"\n🔧 规则质控案例:")
    for i, case in enumerate(rule_cases, 1):
        print(f"  {i}. [{case['rule_type']}] {case['rule_content']}")
    
    # 内涵质控案例
    content_cases = [r for r in results if r['classification'] == '内涵'][:3]
    print(f"\n📋 内涵质控案例:")
    for i, case in enumerate(content_cases, 1):
        print(f"  {i}. [{case['rule_type']}] {case['rule_content']}")

def main():
    """主测试函数"""
    try:
        results, stats = test_classification_on_real_data()
        analyze_classification_patterns(results)
        
        print(f"\n" + "=" * 60)
        print("✅ 真实数据分类测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
