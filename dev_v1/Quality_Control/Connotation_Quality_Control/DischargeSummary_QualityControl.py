"""
出院小结内涵质控模块

该模块实现对出院小结数据的内涵质控功能，主要检查出院医嘱相关内容的完整性。
根据质控规则对出院带药及建议字段进行详细检查，确保包含必要的医疗信息。
使用大模型进行智能化的内涵质控分析。

作者：Augment Agent
创建时间：2025-08-07
版本：1.0
"""

import re
import json
import sys
import os
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..')
sys.path.append(project_root)

from model_use import llm_use, llm_use_concurrent
from config import qwen_32B_config


def quality_control(data: dict) -> list:
    """
    对出院小结数据进行内涵质控

    Args:
        data: 待处理的出院小结数据对象，包含Patient、RepSummaryInfo、VisitInfo三个主要部分

    Returns:
        list: 质控结果列表，每个问题一个字典，格式为：
        [{
            "score": int,  # 扣分分数
            "message": str,  # 具体问题描述
            "suggestions": str  # 改进建议
        }]
    """
    quality_issues = []

    try:
        # 检查数据结构完整性
        if not isinstance(data, dict):
            quality_issues.append({
                "score": 30,
                "message": "数据格式错误：输入数据不是有效的字典格式",
                "suggestions": "请确保输入数据为正确的JSON格式，包含Patient、RepSummaryInfo、VisitInfo三个主要部分"
            })
            return quality_issues

        # 检查RepSummaryInfo部分是否存在
        rep_summary_info = data.get("RepSummaryInfo")
        if not rep_summary_info or not isinstance(rep_summary_info, dict):
            quality_issues.append({
                "score": 30,
                "message": "数据结构错误：缺少RepSummaryInfo部分",
                "suggestions": "请确保数据中包含RepSummaryInfo部分，该部分应包含出院带药及建议等关键信息"
            })
            return quality_issues

        # 检查出院带药及建议字段（DisHospitalDrugs）
        discharge_drugs = rep_summary_info.get("DisHospitalDrugs")

        # 检查字段是否存在、为空或只包含空白字符
        if not discharge_drugs or not discharge_drugs.strip():
            quality_issues.append({
                "score": 30,
                "message": "缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等",
                "suggestions": "请完善出院医嘱内容，应包括：1）出院带药的药名、剂量、用法、带药总量；2）随访要求和时间安排；3）注意事项和异常情况处理指导；4）复查项目和时间安排"
            })
        else:
            # 如果字段存在且不为空，使用大模型进行深度内涵质控检查
            print(f"开始对出院医嘱进行大模型质控检查，内容长度: {len(discharge_drugs)} 字符")
            llm_issues = _llm_based_quality_check(discharge_drugs, data)
            quality_issues.extend(llm_issues)
            print(f"大模型质控检查完成，发现 {len(llm_issues)} 个问题")

    except Exception as e:
        quality_issues.append({
            "score": 30,
            "message": f"质控过程中发生异常：{str(e)}",
            "suggestions": "请检查数据格式是否正确，确保所有必需字段都存在且格式正确"
        })

    return quality_issues


def _llm_based_quality_check(discharge_drugs: str, data: dict) -> List[Dict[str, Any]]:
    """
    使用大模型进行基于内涵的出院医嘱质控检查

    Args:
        discharge_drugs: 出院带药及建议内容
        data: 完整的出院小结数据

    Returns:
        list: 发现的质控问题列表
    """
    issues = []

    try:
        # 构建系统提示词
        system_prompt = _build_system_prompt()

        # 构建用户提示词
        user_prompt = _build_user_prompt(discharge_drugs, data)

        # 调用大模型进行质控分析
        print("正在调用大模型进行质控分析...")
        llm_response = llm_use(system_prompt, user_prompt, qwen_32B_config)

        if llm_response and llm_response.strip():
            print(f"大模型响应成功，响应长度: {len(llm_response)} 字符")
            print(f"大模型响应内容预览: {llm_response[:200]}...")

            # 解析大模型响应，提取质控结果
            parsed_issues = _parse_llm_response(llm_response)

            if parsed_issues:
                issues.extend(parsed_issues)
                print(f"成功解析出 {len(parsed_issues)} 个质控问题")
            else:
                print("大模型响应解析后未发现质控问题，或解析失败，启用备用检查")
                fallback_issues = _fallback_quality_check(discharge_drugs)
                issues.extend(fallback_issues)
        else:
            print("大模型调用失败或返回空响应，启用备用检查")
            # 大模型调用失败时的备用检查
            fallback_issues = _fallback_quality_check(discharge_drugs)
            issues.extend(fallback_issues)

    except Exception as e:
        print(f"大模型质控检查异常：{str(e)}")
        print("异常情况下启用备用检查方法")
        # 异常时使用备用检查方法
        fallback_issues = _fallback_quality_check(discharge_drugs)
        issues.extend(fallback_issues)

    return issues


def _build_system_prompt() -> str:
    """
    构建大模型的系统提示词

    Returns:
        str: 系统提示词
    """
    return """你是一名专业的医疗质控专家，专门负责出院小结的内涵质控工作。你的任务是评估出院医嘱内容的完整性和合理性。

你需要重点检查出院医嘱是否包含以下关键要素：
1. 出院带药信息：药名、剂量、用法、用量、带药总量
2. 随访安排：随访时间、随访科室、复查项目
3. 注意事项：生活指导、异常情况处理、禁忌事项
4. 医疗指导：用药注意事项、复查要求、联系方式

评估标准：
- 如果出院医嘱内容完整、合理，无需扣分
- 如果缺少重要信息或内容不够详细，根据缺失程度扣分（5-30分）
- 如果内容存在明显错误或不合理，扣分更多

**重要要求：你必须严格按照以下JSON格式返回结果，不允许包含任何额外的文本说明、格式化内容或其他字符。只返回纯JSON格式的数据：**

{
    "has_issues": true,
    "issues": [
        {
            "score": 15,
            "message": "具体问题描述",
            "suggestions": "改进建议"
        }
    ]
}

如果没有发现问题，必须返回：
{
    "has_issues": false,
    "issues": []
}

注意：
- score必须是0-100之间的整数
- message和suggestions必须是非空字符串
- 不要在JSON前后添加任何解释性文字
- 不要使用markdown格式或代码块标记
- 确保JSON格式完全有效且可解析"""


def _build_user_prompt(discharge_drugs: str, data: dict) -> str:
    """
    构建大模型的用户提示词

    Args:
        discharge_drugs: 出院带药及建议内容
        data: 完整的出院小结数据

    Returns:
        str: 用户提示词
    """
    # 提取患者基本信息
    patient_info = data.get("Patient", {})
    visit_info = data.get("VisitInfo", {})

    patient_age = patient_info.get("Age", "未知")
    patient_sex = patient_info.get("SexName", "未知")
    dept_name = visit_info.get("DeptName", "未知")

    user_prompt = f"""请对以下出院医嘱内容进行内涵质控评估：

患者基本信息：
- 年龄：{patient_age}岁
- 性别：{patient_sex}
- 科室：{dept_name}

出院医嘱内容：
{discharge_drugs}

请仔细分析上述出院医嘱内容，检查是否包含必要的医疗信息：
1. 出院带药是否写明药名、剂量、用法、带药总量？
2. 是否有明确的随访要求和注意事项交待？
3. 内容是否完整、合理、符合医疗规范？

请根据质控标准进行评估并返回JSON格式的结果。"""

    return user_prompt


def _parse_llm_response(llm_response: str) -> List[Dict[str, Any]]:
    """
    解析大模型响应，提取质控结果

    Args:
        llm_response: 大模型的响应文本

    Returns:
        list: 解析出的质控问题列表
    """
    issues = []
    parse_error_details = None

    try:
        # 清理响应文本，移除可能的格式化字符
        cleaned_response = llm_response.strip()

        # 第一步：尝试直接解析JSON
        try:
            response_data = json.loads(cleaned_response)
            print(f"JSON直接解析成功")
        except json.JSONDecodeError as e:
            parse_error_details = f"直接JSON解析失败: {str(e)}"
            print(f"直接JSON解析失败: {str(e)}")

            # 第二步：尝试提取JSON部分（更强的正则表达式）
            json_patterns = [
                r'\{[^{}]*"has_issues"[^{}]*\}',  # 简单的单层JSON
                r'\{(?:[^{}]|\{[^{}]*\})*\}',     # 支持嵌套的JSON
                r'\{.*?"has_issues".*?\}',        # 包含has_issues的JSON片段
                r'\{.*\}',                        # 最宽泛的JSON匹配
            ]

            response_data = None
            for pattern in json_patterns:
                json_match = re.search(pattern, cleaned_response, re.DOTALL)
                if json_match:
                    try:
                        json_text = json_match.group()
                        response_data = json.loads(json_text)
                        print(f"使用正则表达式成功提取JSON: {pattern}")
                        break
                    except json.JSONDecodeError as pattern_error:
                        print(f"正则表达式 {pattern} 提取的JSON解析失败: {str(pattern_error)}")
                        continue

            if response_data is None:
                raise ValueError(f"所有JSON提取方法都失败。原始响应: {cleaned_response[:200]}...")

        # 第三步：验证JSON结构和内容
        if not isinstance(response_data, dict):
            raise ValueError(f"响应不是字典格式: {type(response_data)}")

        if "has_issues" not in response_data:
            raise ValueError("响应中缺少'has_issues'字段")

        # 第四步：处理质控结果
        has_issues = response_data.get("has_issues", False)
        if has_issues:
            issues_list = response_data.get("issues", [])
            if not isinstance(issues_list, list):
                raise ValueError(f"'issues'字段不是列表格式: {type(issues_list)}")

            for i, issue in enumerate(issues_list):
                if not isinstance(issue, dict):
                    print(f"跳过第{i+1}个问题：不是字典格式")
                    continue

                # 验证必需字段
                required_fields = ["score", "message", "suggestions"]
                missing_fields = [field for field in required_fields if field not in issue]
                if missing_fields:
                    print(f"跳过第{i+1}个问题：缺少字段 {missing_fields}")
                    continue

                # 验证分数
                score = issue.get("score")
                if not isinstance(score, (int, float)):
                    print(f"跳过第{i+1}个问题：分数不是数值类型 {type(score)}")
                    continue

                if not (0 <= score <= 100):
                    print(f"跳过第{i+1}个问题：分数超出范围 {score}")
                    continue

                # 验证消息和建议
                message = issue.get("message", "").strip()
                suggestions = issue.get("suggestions", "").strip()

                if not message:
                    print(f"跳过第{i+1}个问题：消息为空")
                    continue

                if not suggestions:
                    print(f"跳过第{i+1}个问题：建议为空")
                    continue

                # 添加有效的质控问题
                issues.append({
                    "score": int(score),
                    "message": message,
                    "suggestions": suggestions
                })

        print(f"成功解析出 {len(issues)} 个质控问题")

    except Exception as e:
        error_msg = f"解析大模型响应时发生错误: {str(e)}"
        if parse_error_details:
            error_msg += f" | 详细信息: {parse_error_details}"

        print(error_msg)
        print(f"原始响应内容: {llm_response[:500]}...")

        # 解析失败时尝试使用正则表达式提取信息
        fallback_issue = _extract_issue_from_text(llm_response)
        if fallback_issue:
            issues.append(fallback_issue)
            print("使用备用文本提取方法成功提取质控信息")
        else:
            print("备用文本提取方法也失败，将触发关键词检查")

    return issues


def _extract_issue_from_text(text: str) -> Optional[Dict[str, Any]]:
    """
    从文本中提取质控问题信息（备用方法）

    Args:
        text: 包含质控信息的文本

    Returns:
        Optional[Dict]: 提取的质控问题，如果提取失败则返回None
    """
    try:
        # 尝试提取分数（更全面的模式）
        score_patterns = [
            r'扣分[：:]\s*(\d+)',
            r'分数[：:]\s*(\d+)',
            r'扣除\s*(\d+)\s*分',
            r'减\s*(\d+)\s*分',
            r'score[：:]\s*(\d+)',
            r'"score"\s*:\s*(\d+)',
            r'得分[：:]\s*(\d+)',
            r'评分[：:]\s*(\d+)',
        ]

        score = None
        for pattern in score_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                extracted_score = int(match.group(1))
                if 0 <= extracted_score <= 100:  # 验证分数范围
                    score = extracted_score
                    break

        if score is None:
            # 根据问题严重程度推断分数
            if any(keyword in text.lower() for keyword in ['严重', '重大', '缺失', '未包含']):
                score = 30
            elif any(keyword in text.lower() for keyword in ['不完整', '不够', '缺少']):
                score = 15
            elif any(keyword in text.lower() for keyword in ['不规范', '不明确', '模糊']):
                score = 10
            else:
                score = 5  # 默认最小扣分

        # 尝试提取问题描述
        message = "出院医嘱内容存在问题，需要完善"
        message_patterns = [
            r'"message"\s*:\s*"([^"]+)"',
            r'问题[：:]([^。]+)',
            r'缺少([^。]+)',
            r'不完整([^。]+)',
        ]

        for pattern in message_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                extracted_message = match.group(1).strip()
                if len(extracted_message) > 5:  # 确保消息有意义
                    message = extracted_message
                    break

        # 尝试提取改进建议
        suggestions = "请检查出院医嘱是否包含完整的药物信息、随访安排和注意事项"
        suggestion_patterns = [
            r'"suggestions"\s*:\s*"([^"]+)"',
            r'建议[：:]([^。]+)',
            r'应该([^。]+)',
            r'需要([^。]+)',
        ]

        for pattern in suggestion_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                extracted_suggestion = match.group(1).strip()
                if len(extracted_suggestion) > 5:  # 确保建议有意义
                    suggestions = extracted_suggestion
                    break

        # 只有在找到明确问题指示时才返回结果
        problem_indicators = [
            '缺少', '不完整', '问题', '错误', '不规范', '不合理',
            '未包含', '未明确', '缺失', '不够', '严重', '重大'
        ]

        if any(indicator in text for indicator in problem_indicators):
            return {
                "score": score,
                "message": message,
                "suggestions": suggestions
            }

    except Exception as e:
        print(f"从文本提取质控信息时发生错误：{str(e)}")

    return None


def _fallback_quality_check(discharge_drugs: str) -> List[Dict[str, Any]]:
    """
    备用质控检查方法（当大模型调用失败时使用）

    Args:
        discharge_drugs: 出院带药及建议内容

    Returns:
        list: 发现的质控问题列表
    """
    issues = []
    content = discharge_drugs.lower()

    # 检查是否包含药物相关信息的关键词
    drug_keywords = ['药', '片', '胶囊', '注射', '口服', '静脉', '肌注', 'mg', 'ml', 'g']
    has_drug_info = any(keyword in content for keyword in drug_keywords)

    # 检查是否包含随访相关信息的关键词
    followup_keywords = ['随访', '复查', '门诊', '复诊', '预约', '回访']
    has_followup_info = any(keyword in content for keyword in followup_keywords)

    # 检查是否包含注意事项相关信息的关键词
    instruction_keywords = ['注意', '禁', '避免', '如有', '及时', '联系', '就诊']
    has_instruction_info = any(keyword in content for keyword in instruction_keywords)

    # 根据内容完整性给出相应的质控建议
    missing_elements = []

    if not has_drug_info:
        missing_elements.append("具体药物信息（药名、剂量、用法、用量）")

    if not has_followup_info:
        missing_elements.append("随访安排和复查要求")

    if not has_instruction_info:
        missing_elements.append("注意事项和异常情况处理指导")

    # 如果缺少重要元素，给出相应的质控问题
    if missing_elements:
        issues.append({
            "score": 15,  # 内容不完整的扣分
            "message": f"出院医嘱内容不够完整，缺少：{', '.join(missing_elements)}",
            "suggestions": f"建议补充以下内容：{', '.join(missing_elements)}。完整的出院医嘱应包含药物治疗、随访安排、注意事项等全面信息"
        })

    return issues


def extract_score_from_llm_response(llm_response: str) -> Optional[int]:
    """
    从大模型返回结果中提取扣分分数（增强版）

    该函数支持多种格式的分数提取，包括JSON格式和自然语言格式。
    优先从JSON结构中提取，如果失败则使用正则表达式匹配。

    Args:
        llm_response: 大模型的返回结果文本

    Returns:
        Optional[int]: 提取到的扣分分数，如果未找到则返回None
    """
    if not llm_response or not llm_response.strip():
        return None

    try:
        # 第一步：尝试从JSON格式中提取分数
        json_score = _extract_score_from_json(llm_response)
        if json_score is not None:
            return json_score

        # 第二步：使用正则表达式匹配各种文本格式
        text_score = _extract_score_from_text_patterns(llm_response)
        if text_score is not None:
            return text_score

        # 第三步：尝试从数字序列中推断分数
        inferred_score = _infer_score_from_numbers(llm_response)
        if inferred_score is not None:
            return inferred_score

        return None

    except Exception as e:
        print(f"提取分数时发生错误：{str(e)}")
        return None


def _extract_score_from_json(response: str) -> Optional[int]:
    """
    从JSON格式的响应中提取分数

    Args:
        response: 大模型响应文本

    Returns:
        Optional[int]: 提取到的分数，如果失败则返回None
    """
    try:
        # 尝试直接解析JSON
        try:
            data = json.loads(response.strip())
        except json.JSONDecodeError:
            # 尝试提取JSON部分
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())
            else:
                return None

        # 从JSON结构中提取分数
        if isinstance(data, dict):
            # 检查是否有issues数组
            if "issues" in data and isinstance(data["issues"], list):
                scores = []
                for issue in data["issues"]:
                    if isinstance(issue, dict) and "score" in issue:
                        score = issue["score"]
                        if isinstance(score, (int, float)) and 0 <= score <= 100:
                            scores.append(int(score))

                if scores:
                    # 返回最高分数（最严重的问题）
                    return max(scores)

            # 检查是否直接包含score字段
            if "score" in data:
                score = data["score"]
                if isinstance(score, (int, float)) and 0 <= score <= 100:
                    return int(score)

        return None

    except Exception:
        return None


def _extract_score_from_text_patterns(response: str) -> Optional[int]:
    """
    使用正则表达式从文本中提取分数

    Args:
        response: 大模型响应文本

    Returns:
        Optional[int]: 提取到的分数，如果失败则返回None
    """
    # 扩展的正则表达式模式，按优先级排序
    patterns = [
        # JSON格式中的score字段
        r'"score"\s*:\s*(\d+)',
        r"'score'\s*:\s*(\d+)",

        # 明确的扣分表述
        r'扣分[：:]\s*(\d+)',
        r'扣除\s*(\d+)\s*分',
        r'减\s*(\d+)\s*分',
        r'罚\s*(\d+)\s*分',

        # 分数表述
        r'分数[：:]\s*(\d+)',
        r'得分[：:]\s*(\d+)',
        r'评分[：:]\s*(\d+)',
        r'总分[：:]\s*(\d+)',

        # 英文格式
        r'score[：:]\s*(\d+)',
        r'points?[：:]\s*(\d+)',
        r'deduct\s*(\d+)',

        # 数字+分的格式
        r'(\d+)\s*分(?!钟|秒|米|厘米|毫米)',  # 避免匹配时间和长度单位
        r'(\d+)\s*points?',

        # 括号中的分数
        r'\((\d+)分\)',
        r'\[(\d+)分\]',

        # 其他格式
        r'共计\s*(\d+)\s*分',
        r'合计\s*(\d+)\s*分',
    ]

    for pattern in patterns:
        matches = re.findall(pattern, response, re.IGNORECASE)
        if matches:
            for match in matches:
                try:
                    score = int(match)
                    # 验证分数范围的合理性
                    if 0 <= score <= 100:
                        return score
                except ValueError:
                    continue

    return None


def _infer_score_from_numbers(response: str) -> Optional[int]:
    """
    从响应中的数字序列推断可能的分数

    Args:
        response: 大模型响应文本

    Returns:
        Optional[int]: 推断的分数，如果无法推断则返回None
    """
    try:
        # 提取所有数字
        numbers = re.findall(r'\b(\d+)\b', response)
        if not numbers:
            return None

        # 转换为整数并过滤合理范围
        valid_scores = []
        for num_str in numbers:
            try:
                num = int(num_str)
                # 只考虑可能是分数的数字（1-100）
                if 1 <= num <= 100:
                    valid_scores.append(num)
            except ValueError:
                continue

        if not valid_scores:
            return None

        # 如果只有一个有效分数，返回它
        if len(valid_scores) == 1:
            return valid_scores[0]

        # 如果有多个分数，优先选择常见的扣分值
        common_scores = [30, 25, 20, 15, 10, 5]
        for common_score in common_scores:
            if common_score in valid_scores:
                return common_score

        # 否则返回最大值（通常代表最严重的问题）
        return max(valid_scores)

    except Exception:
        return None


if __name__ == "__main__":
    # 测试用例
    print("出院小结内涵质控模块测试")
    print("=" * 50)

    # 测试用例1：正常数据（包含完整出院医嘱）
    test_data_1 = {
        "Patient": {
            "PatientId": "TEST001",
            "PatientName": "测试患者",
            "Age": 45,
            "SexName": "男"
        },
        "RepSummaryInfo": {
            "DisHospitalDrugs": "1.出院带药：阿司匹林100mg，每日1次，口服，带药30天；美托洛尔25mg，每日2次，口服，带药30天；2.门诊随访：心内科门诊1周后复查心电图和血压；3.注意事项：如有胸痛、气短等症状及时就诊；4.生活指导：低盐低脂饮食，适量运动"
        },
        "VisitInfo": {
            "AdmissionNumber": "Y001",
            "DeptName": "心内科"
        }
    }

    # 测试用例2：缺少出院医嘱
    test_data_2 = {
        "Patient": {
            "PatientId": "TEST002",
            "PatientName": "测试患者2",
            "Age": 35,
            "SexName": "女"
        },
        "RepSummaryInfo": {
            "DisHospitalDrugs": ""
        },
        "VisitInfo": {
            "AdmissionNumber": "Y002",
            "DeptName": "妇科"
        }
    }

    # 测试用例3：内容不完整的出院医嘱
    test_data_3 = {
        "Patient": {
            "PatientId": "TEST003",
            "PatientName": "测试患者3",
            "Age": 28,
            "SexName": "女"
        },
        "RepSummaryInfo": {
            "DisHospitalDrugs": "注意休息，门诊随访"
        },
        "VisitInfo": {
            "AdmissionNumber": "Y003",
            "DeptName": "内科"
        }
    }

    print("测试用例1（完整出院医嘱）：")
    result1 = quality_control(test_data_1)
    if result1:
        for issue in result1:
            print(f"  扣分：{issue['score']}")
            print(f"  问题：{issue['message']}")
            print(f"  建议：{issue['suggestions']}")
            print()
    else:
        print("  未发现质控问题")
        print()

    print("测试用例2（缺少出院医嘱）：")
    result2 = quality_control(test_data_2)
    for issue in result2:
        print(f"  扣分：{issue['score']}")
        print(f"  问题：{issue['message']}")
        print(f"  建议：{issue['suggestions']}")
        print()

    print("测试用例3（内容不完整）：")
    result3 = quality_control(test_data_3)
    for issue in result3:
        print(f"  扣分：{issue['score']}")
        print(f"  问题：{issue['message']}")
        print(f"  建议：{issue['suggestions']}")
        print()

    # 测试大模型响应解析功能
    print("测试大模型响应解析功能：")
    test_json_response = '''{"has_issues": true, "issues": [{"score": 20, "message": "出院医嘱缺少具体药物剂量信息", "suggestions": "请补充药物的具体剂量、用法和用量信息"}]}'''
    parsed_issues = _parse_llm_response(test_json_response)
    for issue in parsed_issues:
        print(f"  扣分：{issue['score']}")
        print(f"  问题：{issue['message']}")
        print(f"  建议：{issue['suggestions']}")
        print()

    # 测试分数提取功能
    print("测试分数提取功能：")
    test_responses = [
        "根据检查结果，扣分：30分",
        "质控评分：score: 15",
        "该问题需要扣除25分",
        "评估结果显示减10分"
    ]

    for response in test_responses:
        score = extract_score_from_llm_response(response)
        print(f"  输入：{response}")
        print(f"  提取分数：{score}")
        print()
