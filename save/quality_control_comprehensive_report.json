{"report_title": "医疗质控综合报告", "generated_time": "2024-01-15 10:00:00", "reports": {"discharge_summary": {"patient_info": {"patient_id": "P001", "patient_name": "张三", "document_type": "出院记录"}, "quality_control_results": {"document_type": "出院记录", "document_type_english": "Discharge_Summary", "regulatory_results": {"缺出院记录，或未在出院后24小时内完成": {"rule_type": "时效性", "classification": "手术科室", "deduction_points": 60.0, "has_problem": false, "type": "regulatory"}, "缺诊疗经过": {"rule_type": "段落完整性", "classification": "手术科室", "deduction_points": 6.0, "has_problem": true, "type": "regulatory"}, "缺入院诊断": {"rule_type": "段落完整性", "classification": "手术科室", "deduction_points": 6.0, "has_problem": true, "type": "regulatory"}, "缺入院情况": {"rule_type": "段落完整性", "classification": "手术科室", "deduction_points": 6.0, "has_problem": true, "type": "regulatory"}, "缺出院诊断": {"rule_type": "段落完整性", "classification": "手术科室", "deduction_points": 6.0, "has_problem": true, "type": "regulatory"}, "缺出院情况": {"rule_type": "段落完整性", "classification": "手术科室", "deduction_points": 6.0, "has_problem": true, "type": "regulatory"}, "缺出院日期": {"rule_type": "段落完整性", "classification": "手术科室", "deduction_points": 6.0, "has_problem": true, "type": "regulatory"}, "缺入院日期": {"rule_type": "段落完整性", "classification": "手术科室", "deduction_points": 6.0, "has_problem": true, "type": "regulatory"}, "缺住院医师、主治医师或以上签名": {"rule_type": "签名合理性", "classification": "手术科室", "deduction_points": 3.0, "has_problem": false, "type": "regulatory"}}, "connotation_results": {"缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等": {"rule_type": "段落完整性", "classification": "手术科室", "max_points": 30.0, "score": 25, "problems": "需要检查出院医嘱的完整性", "suggestions": "建议完善出院医嘱，包括药名、剂量、用法等信息", "type": "connotation"}}, "summary": {"total_rules_checked": 10, "regulatory_issues": 7, "connotation_issues": 1, "total_deduction_points": 42.0, "connotation_total_score": 25, "connotation_max_score": 30.0, "overall_status": "中等问题", "key_issues": [{"type": "规则质控", "rule": "缺诊疗经过", "rule_type": "段落完整性", "deduction_points": 6.0}, {"type": "规则质控", "rule": "缺入院诊断", "rule_type": "段落完整性", "deduction_points": 6.0}, {"type": "规则质控", "rule": "缺入院情况", "rule_type": "段落完整性", "deduction_points": 6.0}, {"type": "规则质控", "rule": "缺出院诊断", "rule_type": "段落完整性", "deduction_points": 6.0}, {"type": "规则质控", "rule": "缺出院情况", "rule_type": "段落完整性", "deduction_points": 6.0}, {"type": "规则质控", "rule": "缺出院日期", "rule_type": "段落完整性", "deduction_points": 6.0}, {"type": "规则质控", "rule": "缺入院日期", "rule_type": "段落完整性", "deduction_points": 6.0}, {"type": "内涵质控", "rule": "缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等", "rule_type": "段落完整性", "score": 25, "max_points": 30.0, "problems": "需要检查出院医嘱的完整性"}], "recommendations": ["建议完善出院医嘱，包括药名、剂量、用法等信息"], "connotation_score_rate": 0.8333333333333334}, "timestamp": "2025-08-05T11:26:52.598674"}, "detailed_analysis": {"regulatory_analysis": {"categories": {"段落完整性": 7}, "severity_distribution": {"高": 0, "中": 0, "低": 7}, "most_common_issues": []}, "connotation_analysis": {"average_score_rate": 0.8333333333333334, "categories": {"段落完整性": {"score": 25, "max_score": 30.0}}, "improvement_areas": [{"rule": "缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等", "problems": "需要检查出院医嘱的完整性", "suggestions": "建议完善出院医嘱，包括药名、剂量、用法等信息"}]}}, "action_items": [{"priority": "中", "category": "规则质控", "issue": "缺诊疗经过", "action": "修正段落完整性问题"}, {"priority": "中", "category": "规则质控", "issue": "缺入院诊断", "action": "修正段落完整性问题"}, {"priority": "中", "category": "规则质控", "issue": "缺入院情况", "action": "修正段落完整性问题"}, {"priority": "中", "category": "规则质控", "issue": "缺出院诊断", "action": "修正段落完整性问题"}, {"priority": "中", "category": "规则质控", "issue": "缺出院情况", "action": "修正段落完整性问题"}, {"priority": "中", "category": "规则质控", "issue": "缺出院日期", "action": "修正段落完整性问题"}, {"priority": "中", "category": "规则质控", "issue": "缺入院日期", "action": "修正段落完整性问题"}, {"priority": "中", "category": "内涵质控", "issue": "需要检查出院医嘱的完整性", "action": "建议完善出院医嘱，包括药名、剂量、用法等信息"}], "compliance_score": {"final_score": 58.33, "base_score": 100, "regulatory_deduction": 42.0, "connotation_bonus": 0.33, "grade": "不合格"}}, "initial_progress_note": {"patient_info": {"patient_id": "P002", "patient_name": "李四", "document_type": "首次病程记录"}, "quality_control_results": {"document_type": "首次病程记录", "document_type_english": "Initial_Progress_Note", "regulatory_results": {"首次病程未在患者入院后8小时内完成": {"rule_type": "时效性", "classification": "手术科室", "deduction_points": 60.0, "has_problem": false, "type": "regulatory"}, "首次病程缺病例特点": {"rule_type": "段落完整性", "classification": "手术科室", "deduction_points": 6.0, "has_problem": false, "type": "regulatory"}, "首次病程缺初步诊断": {"rule_type": "段落完整性", "classification": "手术科室", "deduction_points": 6.0, "has_problem": false, "type": "regulatory"}, "首次病程缺诊断依据": {"rule_type": "段落完整性", "classification": "手术科室", "deduction_points": 6.0, "has_problem": false, "type": "regulatory"}, "首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断": {"rule_type": "段落完整性", "classification": "手术科室", "deduction_points": 6.0, "has_problem": false, "type": "regulatory"}, "首次病程缺诊疗计划": {"rule_type": "段落完整性", "classification": "手术科室", "deduction_points": 6.0, "has_problem": false, "type": "regulatory"}}, "connotation_results": {"首次病程记录与（副）主任医师首次查房记录雷同": {"rule_type": "雷同率", "classification": "手术科室", "max_points": 12.0, "score": 10, "problems": "需要检查首次病程记录与主任查房记录的雷同情况", "suggestions": "建议确保首次病程记录具有独特性，避免与查房记录雷同", "type": "connotation"}}, "summary": {"total_rules_checked": 7, "regulatory_issues": 0, "connotation_issues": 1, "total_deduction_points": 0, "connotation_total_score": 10, "connotation_max_score": 12.0, "overall_status": "轻微问题", "key_issues": [{"type": "内涵质控", "rule": "首次病程记录与（副）主任医师首次查房记录雷同", "rule_type": "雷同率", "score": 10, "max_points": 12.0, "problems": "需要检查首次病程记录与主任查房记录的雷同情况"}], "recommendations": ["建议确保首次病程记录具有独特性，避免与查房记录雷同"], "connotation_score_rate": 0.8333333333333334}, "timestamp": "2025-08-05T11:27:21.125956"}, "detailed_analysis": {"regulatory_analysis": {"categories": {}, "severity_distribution": {"高": 0, "中": 0, "低": 0}, "most_common_issues": []}, "connotation_analysis": {"average_score_rate": 0.8333333333333334, "categories": {"雷同率": {"score": 10, "max_score": 12.0}}, "improvement_areas": [{"rule": "首次病程记录与（副）主任医师首次查房记录雷同", "problems": "需要检查首次病程记录与主任查房记录的雷同情况", "suggestions": "建议确保首次病程记录具有独特性，避免与查房记录雷同"}]}}, "action_items": [{"priority": "中", "category": "内涵质控", "issue": "需要检查首次病程记录与主任查房记录的雷同情况", "action": "建议确保首次病程记录具有独特性，避免与查房记录雷同"}], "compliance_score": {"final_score": 100.33, "base_score": 100, "regulatory_deduction": 0, "connotation_bonus": 0.33, "grade": "优秀"}}}, "summary": {"total_patients": 2, "total_documents": 2, "average_compliance_score": 79.33}}