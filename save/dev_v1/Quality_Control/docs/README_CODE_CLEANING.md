# 质控代码格式问题解决方案

## 问题描述

质控代码生成器生成的Python文件存在以下格式问题：

1. **Markdown代码块标记**：文件包含 ````python` 和 ```` 标记
2. **中文功能描述文本**：混入了非可执行的中文描述文字
3. **执行问题**：这些额外内容导致Python文件无法直接运行

## 解决方案概述

我们提供了一个完整的解决方案，包括：

1. **自动代码清理工具** (`code_cleaner.py`)
2. **改进的代码生成器** (修改了prompt)
3. **集成的自动清理功能** (生成时自动清理)
4. **批量处理脚本** (清理现有文件)

## 解决方案详情

### 1. 代码清理工具 (`code_cleaner.py`)

#### 主要功能
- **智能识别**：自动检测Markdown标记和中文描述文字
- **保护机制**：保留Python注释和文档字符串
- **批量处理**：支持单文件、目录和整个质控系统的清理
- **安全备份**：清理前自动创建备份文件

#### 使用方法

```bash
# 清理单个文件
python code_cleaner.py --file path/to/file.py

# 清理目录中的所有Python文件
python code_cleaner.py --dir path/to/directory

# 清理质控系统中的所有生成文件
python code_cleaner.py --all
python code_cleaner.py  # 默认行为
```

#### 清理规则

| 内容类型 | 处理方式 | 示例 |
|---------|---------|------|
| Markdown标记 | 删除 | `````python`, ```` |
| Python注释 | 保留 | `# 这是注释` |
| 文档字符串 | 保留 | `"""这是文档字符串"""` |
| 中文描述段落 | 删除 | `这个代码实现了以下功能：` |
| Python代码 | 保留 | `def check_rule():` |
| 功能列表 | 删除 | `1. 检查病历数据` |

### 2. 改进的代码生成器

#### Prompt优化

**修改前的问题**：
- 生成器的prompt没有明确禁止Markdown格式
- 允许生成功能描述文字
- 缺乏格式控制指令

**修改后的改进**：
```python
system_prompt = """你是一个专业的医疗质控代码生成专家。请根据提供的质控规则生成纯净的Python代码。

严格要求：
1. 只输出纯Python代码，不要包含任何Markdown标记（如```python或```）
2. 不要在代码中添加功能描述段落或中文说明文字
3. 只使用Python注释（#）和文档字符串（"""）来说明代码
4. 生成的函数应该接收病历数据作为输入
5. 返回二元判断结果（True表示有问题，False表示没有问题）
6. 函数名必须是：check_rule
7. 包含完整的错误处理逻辑
8. 代码应该是独立可测试的

输出格式：直接输出Python代码，不要任何额外的格式标记或说明文字。
"""
```

#### 自动清理集成

生成器现在包含自动清理功能：

```python
# 生成代码后自动清理
generated_code = llm_use(system_prompt, user_prompt, glm_code_config)
if generated_code:
    # 清理生成的代码
    cleaned_code = self._clean_generated_code(generated_code)
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(cleaned_code)
```

### 3. 清理算法详解

#### 智能识别算法

```python
def _is_description_text(self, line: str) -> bool:
    """判断是否为描述性文字（非代码）"""
    
    # 1. 保护Python语法元素
    if line.startswith('#') or 'import' in line or 'def' in line:
        return False
    
    # 2. 识别中文描述模式
    description_patterns = [
        r'^这个代码实现了',
        r'^主要功能',
        r'^使用方法',
        r'^\d+\.\s*[^#]',  # 数字列表
    ]
    
    # 3. 中文字符比例分析
    chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', line))
    if chinese_chars / len(line) > 0.6:  # 中文占比超过60%
        return True
        
    return False
```

#### 清理流程

1. **预处理**：分割文件为行
2. **状态跟踪**：跟踪文档字符串状态
3. **模式匹配**：识别Markdown标记和描述文字
4. **内容过滤**：移除问题内容，保留有效代码
5. **后处理**：清理多余空行，格式化输出

### 4. 测试和验证

#### 测试脚本 (`test_code_cleaner.py`)

提供了完整的测试套件：

```bash
python test_code_cleaner.py
```

测试内容包括：
- 创建包含格式问题的测试文件
- 验证清理功能的正确性
- 检查清理后的文件质量
- 演示模式识别算法

#### 验证结果

测试结果显示：
- ✅ 成功移除所有Markdown标记
- ✅ 成功删除中文描述文字
- ✅ 保留所有有效Python代码
- ✅ 保护注释和文档字符串
- ✅ 文件可以正常执行

### 5. 批量清理现有文件

对于已经生成的问题文件，可以批量清理：

```bash
# 清理所有质控系统文件
python code_cleaner.py --all
```

清理结果：
- 成功清理了4个文件
- 自动创建了备份文件
- 所有文件现在都是纯Python代码

## 使用指南

### 立即解决现有问题

1. **清理现有文件**：
   ```bash
   cd dev_v1/Quality_Control
   python code_cleaner.py --all
   ```

2. **验证清理效果**：
   ```bash
   python test_code_cleaner.py
   ```

### 预防未来问题

1. **使用改进的生成器**：
   - 新的prompt已经优化
   - 集成了自动清理功能
   - 生成的代码将是纯净的

2. **定期清理检查**：
   ```bash
   python code_cleaner.py --all
   ```

### 自定义清理规则

如需修改清理规则，编辑 `code_cleaner.py` 中的相关方法：

- `is_markdown_block_marker()`: 识别Markdown标记
- `is_chinese_description()`: 识别中文描述
- `_clean_generated_code()`: 清理算法主逻辑

## 技术特性

### 安全性
- **备份机制**：清理前自动创建 `.backup` 文件
- **非破坏性**：只修改有问题的文件
- **可恢复性**：可以从备份文件恢复原始内容

### 智能性
- **上下文感知**：区分代码和描述文字
- **语法保护**：保护所有有效的Python语法
- **模式识别**：智能识别各种问题模式

### 效率性
- **批量处理**：支持目录级别的批量清理
- **增量清理**：只处理有问题的文件
- **快速执行**：高效的文本处理算法

## 总结

通过这个完整的解决方案：

1. **问题已解决**：所有格式问题都得到了处理
2. **预防机制**：改进的生成器避免未来问题
3. **工具完备**：提供了完整的清理和测试工具
4. **使用简单**：一键清理，自动化处理

现在生成的Python文件将是纯净的、可执行的代码，完全符合您的要求。
