# 质控系统增强功能说明

## 概述

本次修改对 `quality_control_main.py` 文件进行了重大增强，添加了对内涵质控和规则质控的独立调用支持，同时保持了原有的综合质控功能。

## 新增功能

### 1. 独立的质控类型调用

#### 1.1 内涵质控（Content Quality Control）
- **功能**: 基于LLM的医疗文档内容质量评估
- **方法**: `run_content_quality_control(document_type_chinese, medical_record)`
- **特点**: 关注医疗记录的专业性、完整性和准确性
- **返回**: 包含内涵质控结果和元数据的字典

#### 1.2 规则质控（Rule-based Quality Control）
- **功能**: 基于预定义规则的合规性检查
- **方法**: `run_rule_quality_control(document_type_chinese, medical_record)`
- **特点**: 确保文档符合医疗规范和标准
- **返回**: 包含规则质控结果和元数据的字典

#### 1.3 综合质控（Integrated Quality Control）
- **功能**: 整合内涵质控和规则质控的全面质量评估
- **方法**: `run_integrated_quality_control(document_type_chinese, medical_record)`
- **特点**: 提供最全面的质控评估
- **返回**: 包含两种质控结果的综合报告

### 2. 支持的文档类型

- **出院小结** (Discharge Summary)
- **初次病程记录** (Initial Progress Note)

### 3. 增强的信息查询功能

#### 3.1 文档类型查询
```python
supported_types = qc_main.get_supported_document_types()
```
返回支持的文档类型列表和映射关系。

#### 3.2 质控类型查询
```python
qc_types = qc_main.get_supported_quality_control_types()
```
返回支持的质控类型及其详细描述。

### 4. 增强的质控摘要功能

- 更详细的质控结果统计
- 质量等级评定（优秀、良好、合格、需改进、不合格）
- 合规率和得分率计算
- 改进建议汇总

## 使用示例

### 基本使用

```python
from quality_control_main import QualityControlMain

# 初始化质控系统
qc_main = QualityControlMain()

# 准备病历数据
medical_record = {
    "patient_id": "P001",
    "patient_name": "张三",
    "content": "病历内容...",
    "admission_date": "2024-08-01",
    "discharge_date": "2024-08-05"
}

# 1. 内涵质控
content_result = qc_main.run_content_quality_control("出院记录", medical_record)

# 2. 规则质控
rule_result = qc_main.run_rule_quality_control("出院记录", medical_record)

# 3. 综合质控
integrated_result = qc_main.run_integrated_quality_control("出院记录", medical_record)

# 生成质控摘要
summary = qc_main.get_quality_control_summary(integrated_result)
```

### 查询支持的功能

```python
# 查询支持的文档类型
supported_types = qc_main.get_supported_document_types()
print(f"支持 {supported_types['total_count']} 种文档类型")

# 查询支持的质控类型
qc_types = qc_main.get_supported_quality_control_types()
for type_key, type_name in qc_types["types"].items():
    print(f"{type_name}: {qc_types['descriptions'][type_key]}")
```

## 返回数据结构

### 内涵质控结果
```json
{
  "connotation_results": {
    "规则内容": {
      "rule_id": "rule_xxx",
      "rule_type": "段落完整性",
      "classification": "手术科室",
      "score": 25,
      "problems": "问题描述",
      "suggestions": "改进建议",
      "type": "connotation"
    }
  },
  "metadata": {
    "document_type_chinese": "出院记录",
    "document_type_english": "Discharge Summary",
    "quality_control_type": "内涵质控",
    "total_rules": 1,
    "timestamp": "2024-08-05T17:00:00"
  }
}
```

### 规则质控结果
```json
{
  "regulatory_results": {
    "规则内容": {
      "rule_id": "rule_xxx",
      "rule_type": "时效性",
      "classification": "手术科室",
      "deduction_points": 60.0,
      "has_problem": false,
      "type": "regulatory"
    }
  },
  "metadata": {
    "document_type_chinese": "出院记录",
    "document_type_english": "Discharge Summary",
    "quality_control_type": "规则质控",
    "total_rules": 1,
    "timestamp": "2024-08-05T17:00:00"
  }
}
```

### 综合质控摘要
```json
{
  "document_info": {...},
  "overall_score": 85.5,
  "quality_grade": "良好",
  "problem_count": 2,
  "regulatory_summary": {
    "total_rules": 10,
    "problem_count": 1,
    "total_deductions": 15.0,
    "compliance_rate": 90.0
  },
  "connotation_summary": {
    "total_rules": 5,
    "total_score": 120,
    "max_score": 150,
    "score_rate": 80.0,
    "problem_count": 1
  },
  "recommendations": ["改进建议1", "改进建议2"]
}
```

## 测试文件

### 1. `simple_test.py`
基本功能测试，验证各个质控类型的基本调用。

### 2. `test_enhanced_quality_control.py`
详细的功能测试，包含错误处理和结果分析。

### 3. `usage_example.py`
完整的使用示例，展示如何在实际场景中使用质控系统。

## 向后兼容性

- 保留了原有的 `run_quality_control()` 方法，默认调用综合质控
- 所有原有的API接口保持不变
- 现有的调用代码无需修改即可继续使用

## 技术改进

1. **类型注解**: 添加了完整的类型注解，提高代码可读性
2. **错误处理**: 增强了错误处理和异常捕获
3. **日志输出**: 改进了日志输出格式，使用表情符号增强可读性
4. **文档字符串**: 添加了详细的文档字符串和使用说明
5. **代码结构**: 优化了代码组织结构，提高了可维护性

## 质量等级评定标准

- **优秀** (95-100分): 文档质量极高，无明显问题
- **良好** (85-94分): 文档质量较好，有轻微问题
- **合格** (75-84分): 文档质量达标，有一些需要改进的地方
- **需改进** (60-74分): 文档质量不够理想，需要重点改进
- **不合格** (0-59分): 文档质量严重不达标，需要重新编写

## 注意事项

1. 确保相关的质控模块已正确安装和配置
2. 内涵质控需要LLM模型支持，确保模型配置正确
3. 规则质控依赖于预定义的规则配置文件
4. 建议在生产环境中使用综合质控获得最全面的评估结果
