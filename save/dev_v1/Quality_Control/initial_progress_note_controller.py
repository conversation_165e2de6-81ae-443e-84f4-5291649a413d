# -*- coding: utf-8 -*-
"""
首次病程记录质控控制器
整合规则质控和内涵质控结果
"""
import sys
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))

class InitialProgressNoteController:
    def __init__(self):
        self.document_type = "Initial_Progress_Note"
        self.regulatory_module = None
        self.connotation_module = None
        self._load_modules()
    
    def _load_modules(self):
        """加载规则质控和内涵质控模块"""
        try:
            # 导入规则质控模块
            regulatory_path = f"Regulatory_Quality_Control.{self.document_type}.initial_progress_note_controller"
            self.regulatory_module = __import__(regulatory_path, fromlist=['run_regulatory_quality_control'])
            print(f"成功加载规则质控模块: {regulatory_path}")
        except ImportError as e:
            print(f"无法加载规则质控模块: {e}")

        try:
            # 导入内涵质控模块
            connotation_path = f"Connotation_Quality_Control.{self.document_type}.initial_progress_note_controller"
            self.connotation_module = __import__(connotation_path, fromlist=['run_connotation_quality_control'])
            print(f"成功加载内涵质控模块: {connotation_path}")
        except ImportError as e:
            print(f"无法加载内涵质控模块: {e}")
    
    def run_regulatory_quality_control(self, medical_record):
        """运行规则质控"""
        if not self.regulatory_module:
            return {"error": "规则质控模块未加载"}
        
        try:
            return self.regulatory_module.run_regulatory_quality_control(medical_record)
        except Exception as e:
            return {"error": f"规则质控执行失败: {str(e)}"}
    
    def run_connotation_quality_control(self, medical_record):
        """运行内涵质控"""
        if not self.connotation_module:
            return {"error": "内涵质控模块未加载"}
        
        try:
            return self.connotation_module.run_connotation_quality_control(medical_record)
        except Exception as e:
            return {"error": f"内涵质控执行失败: {str(e)}"}
    
    def run_integrated_quality_control(self, medical_record):
        """
        运行整合质控检查
        
        Args:
            medical_record (dict): 病历数据
            
        Returns:
            dict: 整合的质控结果
        """
        results = {
            "document_type": "首次病程记录",
            "document_type_english": self.document_type,
            "regulatory_results": {},
            "connotation_results": {},
            "summary": {},
            "timestamp": None
        }
        
        # 运行规则质控
        print("执行规则质控...")
        regulatory_results = self.run_regulatory_quality_control(medical_record)
        if "error" not in regulatory_results:
            results["regulatory_results"] = regulatory_results
        else:
            results["regulatory_error"] = regulatory_results["error"]
        
        # 运行内涵质控
        print("执行内涵质控...")
        connotation_results = self.run_connotation_quality_control(medical_record)
        if "error" not in connotation_results:
            results["connotation_results"] = connotation_results
        else:
            results["connotation_error"] = connotation_results["error"]
        
        # 生成综合摘要
        results["summary"] = self._generate_summary(results)
        
        # 添加时间戳
        from datetime import datetime
        results["timestamp"] = datetime.now().isoformat()
        
        return results
    
    def _generate_summary(self, results):
        """生成质控结果摘要"""
        summary = {
            "total_rules_checked": 0,
            "regulatory_issues": 0,
            "connotation_issues": 0,
            "total_deduction_points": 0,
            "connotation_total_score": 0,
            "connotation_max_score": 0,
            "overall_status": "通过",
            "key_issues": [],
            "recommendations": []
        }
        
        # 统计规则质控结果
        regulatory_results = results.get("regulatory_results", {})
        for rule, result in regulatory_results.items():
            summary["total_rules_checked"] += 1
            if result.get("has_problem", False):
                summary["regulatory_issues"] += 1
                summary["total_deduction_points"] += result.get("deduction_points", 0)
                summary["key_issues"].append({
                    "type": "规则质控",
                    "rule": rule,
                    "rule_type": result.get("rule_type", ""),
                    "deduction_points": result.get("deduction_points", 0)
                })
        
        # 统计内涵质控结果
        connotation_results = results.get("connotation_results", {})
        for rule, result in connotation_results.items():
            summary["total_rules_checked"] += 1
            summary["connotation_total_score"] += result.get("score", 0)
            summary["connotation_max_score"] += result.get("max_points", 0)
            
            if result.get("problems"):
                summary["connotation_issues"] += 1
                summary["key_issues"].append({
                    "type": "内涵质控",
                    "rule": rule,
                    "rule_type": result.get("rule_type", ""),
                    "score": result.get("score", 0),
                    "max_points": result.get("max_points", 0),
                    "problems": result.get("problems", "")
                })
                
                if result.get("suggestions"):
                    summary["recommendations"].append(result.get("suggestions"))
        
        # 判断总体状态
        if summary["regulatory_issues"] > 0 or summary["connotation_issues"] > 0:
            if summary["total_deduction_points"] >= 60:
                summary["overall_status"] = "严重问题"
            elif summary["total_deduction_points"] >= 30:
                summary["overall_status"] = "中等问题"
            else:
                summary["overall_status"] = "轻微问题"
        
        # 计算内涵质控得分率
        if summary["connotation_max_score"] > 0:
            summary["connotation_score_rate"] = summary["connotation_total_score"] / summary["connotation_max_score"]
        else:
            summary["connotation_score_rate"] = 1.0
        
        return summary
    
    def get_quality_control_report(self, medical_record):
        """
        生成详细的质控报告
        
        Args:
            medical_record (dict): 病历数据
            
        Returns:
            dict: 详细的质控报告
        """
        results = self.run_integrated_quality_control(medical_record)
        
        report = {
            "patient_info": {
                "patient_id": medical_record.get("patient_id", ""),
                "patient_name": medical_record.get("patient_name", ""),
                "document_type": "首次病程记录"
            },
            "quality_control_results": results,
            "detailed_analysis": {
                "regulatory_analysis": self._analyze_regulatory_results(results.get("regulatory_results", {})),
                "connotation_analysis": self._analyze_connotation_results(results.get("connotation_results", {}))
            },
            "action_items": self._generate_action_items(results),
            "compliance_score": self._calculate_compliance_score(results)
        }
        
        return report
    
    def _analyze_regulatory_results(self, regulatory_results):
        """分析规则质控结果"""
        analysis = {
            "categories": {},
            "severity_distribution": {"高": 0, "中": 0, "低": 0},
            "most_common_issues": []
        }
        
        for rule, result in regulatory_results.items():
            if result.get("has_problem", False):
                category = result.get("rule_type", "其他")
                if category not in analysis["categories"]:
                    analysis["categories"][category] = 0
                analysis["categories"][category] += 1
                
                # 根据扣分判断严重程度
                deduction = result.get("deduction_points", 0)
                if deduction >= 30:
                    analysis["severity_distribution"]["高"] += 1
                elif deduction >= 10:
                    analysis["severity_distribution"]["中"] += 1
                else:
                    analysis["severity_distribution"]["低"] += 1
        
        return analysis
    
    def _analyze_connotation_results(self, connotation_results):
        """分析内涵质控结果"""
        analysis = {
            "average_score_rate": 0,
            "categories": {},
            "improvement_areas": []
        }
        
        total_score = 0
        max_score = 0
        
        for rule, result in connotation_results.items():
            total_score += result.get("score", 0)
            max_score += result.get("max_points", 0)
            
            category = result.get("rule_type", "其他")
            if category not in analysis["categories"]:
                analysis["categories"][category] = {"score": 0, "max_score": 0}
            
            analysis["categories"][category]["score"] += result.get("score", 0)
            analysis["categories"][category]["max_score"] += result.get("max_points", 0)
            
            if result.get("problems"):
                analysis["improvement_areas"].append({
                    "rule": rule,
                    "problems": result.get("problems", ""),
                    "suggestions": result.get("suggestions", "")
                })
        
        if max_score > 0:
            analysis["average_score_rate"] = total_score / max_score
        
        return analysis
    
    def _generate_action_items(self, results):
        """生成行动项目"""
        action_items = []
        
        # 基于规则质控结果生成行动项目
        regulatory_results = results.get("regulatory_results", {})
        for rule, result in regulatory_results.items():
            if result.get("has_problem", False):
                action_items.append({
                    "priority": "高" if result.get("deduction_points", 0) >= 30 else "中",
                    "category": "规则质控",
                    "issue": rule,
                    "action": f"修正{result.get('rule_type', '')}问题"
                })
        
        # 基于内涵质控结果生成行动项目
        connotation_results = results.get("connotation_results", {})
        for rule, result in connotation_results.items():
            if result.get("problems"):
                action_items.append({
                    "priority": "中",
                    "category": "内涵质控",
                    "issue": result.get("problems", ""),
                    "action": result.get("suggestions", "")
                })
        
        return action_items
    
    def _calculate_compliance_score(self, results):
        """计算合规评分"""
        summary = results.get("summary", {})
        
        # 基础分100分
        base_score = 100
        
        # 扣除规则质控问题分数
        regulatory_deduction = summary.get("total_deduction_points", 0)
        
        # 内涵质控得分率影响
        connotation_rate = summary.get("connotation_score_rate", 1.0)
        connotation_bonus = (connotation_rate - 0.8) * 10 if connotation_rate > 0.8 else 0
        
        final_score = max(0, base_score - regulatory_deduction + connotation_bonus)
        
        return {
            "final_score": round(final_score, 2),
            "base_score": base_score,
            "regulatory_deduction": regulatory_deduction,
            "connotation_bonus": round(connotation_bonus, 2),
            "grade": self._get_grade(final_score)
        }
    
    def _get_grade(self, score):
        """根据分数获取等级"""
        if score >= 95:
            return "优秀"
        elif score >= 85:
            return "良好"
        elif score >= 75:
            return "合格"
        elif score >= 60:
            return "基本合格"
        else:
            return "不合格"

# 为了兼容主控制器的调用
def run_integrated_quality_control(medical_record):
    """兼容性函数"""
    controller = InitialProgressNoteController()
    return controller.run_integrated_quality_control(medical_record)

if __name__ == "__main__":
    # 测试示例
    controller = InitialProgressNoteController()
    
    test_record = {
        "patient_id": "12345",
        "patient_name": "张三",
        "content": "首次病程记录测试内容...",
        "admission_date": "2024-01-01",
        "admission_time": "08:00"
    }
    
    report = controller.get_quality_control_report(test_record)
    print("首次病程记录质控报告:")
    print(f"合规评分: {report['compliance_score']}")
    print(f"行动项目数量: {len(report['action_items'])}")
