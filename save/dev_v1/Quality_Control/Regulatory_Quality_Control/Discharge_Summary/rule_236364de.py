# Rule ID: rule_236364de
# Description: Missing discharge summary or not completed within 24 hours after discharge

import datetime

def check_rule(medical_record):
    """
    Check if discharge summary is missing or not completed within 24 hours after discharge
    Returns True if rule is violated, False otherwise
    """
    try:
        # Check if discharge summary exists
        if 'discharge_summary' not in medical_record:
            return True
            
        # Get discharge time and record completion time
        discharge_time = medical_record['discharge_time']
        completion_time = medical_record['record_completion_time']
        
        # Parse datetime strings (assuming ISO format)
        discharge_dt = datetime.datetime.fromisoformat(discharge_time)
        completion_dt = datetime.datetime.fromisoformat(completion_time)
        
        # Calculate time difference
        time_diff = completion_dt - discharge_dt
        
        # Check if more than 24 hours
        if time_diff.total_seconds() > 24*3600:
            return True
            
        return False
            
    except KeyError as e:
        # Missing required field
        print(f"Missing field: {e}")
        return True
    except ValueError as e:
        # Invalid datetime format
        print(f"Invalid datetime format: {e}")
        return True
    except Exception as e:
        # Any other unexpected errors
        print(f"Error processing rule: {e}")
        return True

# Test cases
if __name__ == "__main__":
    # Test case 1: Missing discharge summary
    test1 = {
        'patient_id': 'P123',
        'admission_date': '2023-01-01',
        'discharge_date': '2023-01-05'
    }
    print("Test 1 (missing summary):", check_rule(test1))  # Expected: True
    
    # Test case 2: Completed within 24 hours
    test2 = {
        'patient_id': 'P124',
        'admission_date': '2023-01-01',
        'discharge_date': '2023-01-05',
        'discharge_summary': 'Complete',
        'discharge_time': '2023-01-05T10:00:00',
        'record_completion_time': '2023-01-05T11:00:00'
    }
    print("Test 2 (within 24h):", check_rule(test2))  # Expected: False
    
    # Test case 3: Completed after 24 hours
    test3 = {
        'patient_id': 'P125',
        'admission_date': '2023-01-01',
        'discharge_date': '2023-01-05',
        'discharge_summary': 'Complete',
        'discharge_time': '2023-01-05T10:00:00',
        'record_completion_time': '2023-01-06T11:00:00'
    }
    print("Test 3 (after 24h):", check_rule(test3))  # Expected: True
    
    # Test case 4: Invalid datetime format
    test4 = {
        'patient_id': 'P126',
        'admission_date': '2023-01-01',
        'discharge_date': '2023-01-05',
        'discharge_summary': 'Complete',
        'discharge_time': '2023-01-05T10:00:00',
        'record_completion_time': 'invalid-time'
    }
    print("Test 4 (invalid datetime):", check_rule(test4))  # Expected: True