"""
Rule ID: rule_2415e6ae
Description: 首次病程缺诊断依据
"""
import re

def check_rule(medical_record):
    """
    检查首次病程记录中是否缺少诊断依据
    返回True表示存在问题（缺诊断依据），False表示符合要求
    """
    try:
        # 提取首次病程记录部分（支持跨行匹配）
        match = re.search(
            r'首次病程记录(.*?)(?=\n## |$)', 
            str(medical_record), 
            re.DOTALL
        )
        
        if not match:
            # 未找到首次病程记录部分
            return True
            
        first_course_content = match.group(1)
        
        # 检查是否存在诊断依据相关内容
        # 使用不区分大小写的模糊匹配
        if not re.search(r'诊断依据|依据分析|确诊依据', first_course_content, re.IGNORECASE):
            return True
            
        return False
            
    except Exception as e:
        # 任何异常都视为规则不适用
        return False

if __name__ == "__main__":
    # 测试用例1: 包含诊断依据
    test1 = """## 首次病程记录
患者男性，50岁。主诉... 诊断依据：根据患者症状..."""
    print(check_rule(test1))  # 应返回False
    
    # 测试用例2: 缺少诊断依据
    test2 = """## 首次病程记录
患者男性，50岁。主诉... 治疗方案..."""
    print(check_rule(test2))  # 应返回True
    
    # 测试用例3: 没有首次病程记录
    test3 = """## 病历特点
患者..."""
    print(check_rule(test3))  # 应返回True
    
    # 测试用例4: 输入非字符串类型
    test4 = 12345
    print(check_rule(test4))  # 应返回False