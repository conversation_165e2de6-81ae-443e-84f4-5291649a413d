# 规则ID: rule_ff5a0e2b
# 描述: 首次病程缺诊疗计划

def check_rule(medical_record):
    """
    检查首次病程记录是否缺少诊疗计划
    返回True表示存在问题，False表示无问题
    """
    try:
        # 假设medical_record是包含病历信息的字典
        initial_course = medical_record.get('initial_course', '')
        
        # 检查诊疗计划相关关键词
        plan_keywords = ['诊疗计划', '治疗方案', '处理措施', '下一步计划']
        
        # 如果首次病程记录为空或不包含任何关键词则判定为问题
        if not initial_course or not any(kw in initial_course for kw in plan_keywords):
            return True
        return False
            
    except Exception as e:
        # 出现异常时返回False（可按实际需求调整处理逻辑）
        return False

# 测试代码
if __name__ == "__main__":
    # 测试用例1: 正常情况
    test1 = {
        'initial_course': '患者入院后完善检查，制定个体化诊疗计划，给予抗感染治疗。'
    }
    print("Test1 (正常情况):", check_rule(test1))  # 应返回False
    
    # 测试用例2: 缺失诊疗计划
    test2 = {
        'initial_course': '患者入院后完善血常规、尿常规检查。'
    }
    print("Test2 (缺失计划):", check_rule(test2))  # 应返回True
    
    # 测试用例3: 异常输入
    test3 = {
        'initial_course': 12345  # 非字符串类型
    }
    print("Test3 (异常输入):", check_rule(test3))  # 应返回False
    
    # 测试用例4: 空值情况
    test4 = {
        'initial_course': ''
    }
    print("Test4 (空值):", check_rule(test4))  # 应返回True