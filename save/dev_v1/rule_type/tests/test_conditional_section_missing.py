#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
条件性章节缺失分类测试
专门测试带有条件限制的章节缺失规则的分类
"""

import sys
import os

# 添加父目录到路径以导入核心模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from medical_document_standards import (
    classify_rule_by_standards,
    is_structural_issue,
    is_content_issue
)

def test_conditional_section_missing():
    """测试条件性章节缺失的分类"""
    print("🔬 条件性章节缺失分类测试")
    print("=" * 60)
    
    # 测试案例
    test_cases = [
        {
            "name": "问题规则：带条件限制的鉴别诊断缺失",
            "rule": {
                "rule_content": "首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断",
                "rule_type_chinese": "段落完整性",
                "document_type_chinese": "首次病程记录",
                "belonging_project_chinese": "病程记录"
            },
            "expected": "规则",
            "reason": "虽然有条件限制，但核心是整个鉴别诊断章节的缺失，属于结构完整性问题"
        },
        {
            "name": "对比案例1：简单的章节缺失",
            "rule": {
                "rule_content": "首次病程缺鉴别诊断",
                "rule_type_chinese": "段落完整性",
                "document_type_chinese": "首次病程记录",
                "belonging_project_chinese": "病程记录"
            },
            "expected": "规则",
            "reason": "明确的章节缺失"
        },
        {
            "name": "对比案例2：章节内容不充分",
            "rule": {
                "rule_content": "鉴别诊断内容不充分",
                "rule_type_chinese": "内容完整性",
                "document_type_chinese": "首次病程记录",
                "belonging_project_chinese": "病程记录"
            },
            "expected": "内涵",
            "reason": "章节存在但内容不充分，属于内容质量问题"
        },
        {
            "name": "类似案例：其他带条件限制的章节缺失",
            "rule": {
                "rule_content": "缺诊断依据，明确诊断的病人可不判断",
                "rule_type_chinese": "段落完整性",
                "document_type_chinese": "首次病程记录",
                "belonging_project_chinese": "病程记录"
            },
            "expected": "规则",
            "reason": "带条件限制的章节缺失仍然是结构性问题"
        }
    ]
    
    correct_count = 0
    total_count = len(test_cases)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. {case['name']}")
        print("-" * 50)
        
        rule = case['rule']
        rule_content = rule['rule_content']
        rule_type = rule['rule_type_chinese']
        
        # 测试结构性问题检测
        is_structural = is_structural_issue(rule_content, rule_type)
        is_content = is_content_issue(rule_content, rule_type)
        
        # 测试标准化分类
        classification = classify_rule_by_standards(rule)
        
        # 检查结果
        is_correct = classification == case['expected']
        status = "✅" if is_correct else "❌"
        
        if is_correct:
            correct_count += 1
        
        print(f"📋 规则内容: {rule_content}")
        print(f"🔍 结构性问题: {is_structural}")
        print(f"🔍 内容性问题: {is_content}")
        print(f"{status} 分类结果: {classification} (期望: {case['expected']})")
        print(f"💡 分类依据: {case['reason']}")
    
    # 总结
    print(f"\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    print(f"测试案例总数: {total_count}")
    print(f"正确分类数量: {correct_count}")
    print(f"分类准确率: {correct_count/total_count*100:.1f}%")
    
    if correct_count == total_count:
        print("🎉 所有测试案例都通过了！条件性章节缺失问题已修复。")
    else:
        print("⚠️  仍有测试案例未通过，需要进一步调整分类逻辑。")
    
    return correct_count == total_count

def test_pattern_matching():
    """测试模式匹配的具体效果"""
    print(f"\n" + "=" * 60)
    print("🔍 模式匹配测试")
    print("=" * 60)
    
    test_patterns = [
        "首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断",
        "缺鉴别诊断，可不判断",
        "缺诊断依据，明确诊断的病人可不判断",
        "首次病程缺病例特点",
        "鉴别诊断内容不充分"
    ]
    
    for pattern in test_patterns:
        is_structural = is_structural_issue(pattern, "段落完整性")
        icon = "🔧" if is_structural else "📋"
        classification = "规则质控" if is_structural else "内涵质控"
        print(f"{icon} {pattern} → {classification}")

def main():
    """主测试函数"""
    try:
        success = test_conditional_section_missing()
        test_pattern_matching()
        
        print(f"\n" + "=" * 60)
        if success:
            print("✅ 条件性章节缺失分类修复成功！")
        else:
            print("❌ 修复未完全成功，需要进一步调整。")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
