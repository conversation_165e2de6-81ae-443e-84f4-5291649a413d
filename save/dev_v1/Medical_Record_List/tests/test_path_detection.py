# -*- coding: utf-8 -*-
"""
测试路径检测功能
"""

import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/..")

from medical_record_list_generator import _find_excel_file, _get_default_excel_path

def test_path_detection():
    """测试路径检测功能"""
    print("=" * 60)
    print("测试路径检测功能")
    print("=" * 60)
    
    print(f"当前工作目录: {os.getcwd()}")
    print(f"当前脚本位置: {os.path.abspath(__file__)}")
    
    # 测试智能文件查找
    print("\n=== 测试智能文件查找 ===")
    found_path = _find_excel_file()
    if found_path:
        print(f"✅ 智能找到Excel文件: {found_path}")
        print(f"   文件存在: {os.path.exists(found_path)}")
        print(f"   文件大小: {os.path.getsize(found_path)} bytes")
    else:
        print("❌ 未能智能找到Excel文件")
    
    # 测试默认路径获取
    print("\n=== 测试默认路径获取 ===")
    default_path, display_path = _get_default_excel_path()
    print(f"默认路径: {default_path}")
    print(f"显示路径: {display_path}")
    print(f"路径存在: {os.path.exists(default_path)}")
    
    # 测试各种可能的路径
    print("\n=== 测试各种可能的路径 ===")
    excel_filename = "附件1. 质控评分表v2.0.xlsx"
    test_paths = [
        f"doc/{excel_filename}",
        f"../doc/{excel_filename}",
        f"../../doc/{excel_filename}",
        f"../../../doc/{excel_filename}",
        f"../../../../doc/{excel_filename}",
    ]
    
    for path in test_paths:
        exists = os.path.exists(path)
        status = "✅" if exists else "❌"
        print(f"   {status} {path}")
    
    # 显示目录结构
    print("\n=== 当前目录结构 ===")
    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")
    
    # 向上查找几级目录
    check_dir = current_dir
    for level in range(5):
        if level > 0:
            check_dir = os.path.dirname(check_dir)
        
        doc_dir = os.path.join(check_dir, "doc")
        excel_file = os.path.join(doc_dir, excel_filename)
        
        doc_exists = os.path.exists(doc_dir)
        excel_exists = os.path.exists(excel_file)
        
        print(f"   Level {level}: {check_dir}")
        print(f"     doc目录存在: {doc_exists}")
        print(f"     Excel文件存在: {excel_exists}")
        
        if excel_exists:
            print(f"     ✅ 找到文件: {excel_file}")
            break
    
    print("\n路径检测测试完成！")

if __name__ == "__main__":
    test_path_detection()
