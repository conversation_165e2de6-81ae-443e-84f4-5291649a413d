# -*- coding: utf-8 -*-
"""
测试医疗记录列表生成器
"""

import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/..")

from medical_record_list_generator import MedicalRecordListGenerator
from config import glm_code_config

def test_generator():
    """测试生成器"""
    print("开始测试医疗记录列表生成器...")
    
    # 设置参数
    excel_path = "../../../doc/附件1. 质控评分表v2.0.xlsx"
    model_config = glm_code_config
    output_dir = "Medical_Record_List"
    
    # 创建生成器实例
    generator = MedicalRecordListGenerator()
    
    # 处理Excel文件
    success = generator.process_excel_file(excel_path, model_config, output_dir)
    
    if success:
        # 显示处理摘要
        summary = generator.get_processing_summary()
        print("\n" + "=" * 60)
        print("处理摘要")
        print("=" * 60)
        print(f"总记录数: {summary['total_records']}")
        print(f"文书类型数: {len(summary['document_types'])}")
        
        print("\n类别统计:")
        for category, stats in summary['category_statistics'].items():
            print(f"  {category}: {stats['unique_count']} 个唯一值")
        
        print("\n翻译统计:")
        for category, count in summary['translation_statistics'].items():
            print(f"  {category}: {count} 个翻译")
        
        print(f"\n前10个文书类型:")
        for doc_type in summary['document_types'][:10]:
            print(f"  - {doc_type}")
        if len(summary['document_types']) > 10:
            print(f"  ... 还有 {len(summary['document_types']) - 10} 个")
        
        print(f"\nJSON文件已保存到: {output_dir}")
        print("测试完成！")
    else:
        print("测试失败，请查看日志了解详细信息")

if __name__ == "__main__":
    test_generator()
