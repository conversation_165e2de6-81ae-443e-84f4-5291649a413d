# -*- coding: utf-8 -*-
"""
测试JSON文件输出路径修改
"""

import sys
import os
import shutil

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/..")

from medical_record_list_generator import MedicalRecordListGenerator
from config import deepseek_v3_config

def test_json_output_path():
    """测试JSON文件输出路径修改"""
    print("=" * 80)
    print("测试JSON文件输出路径修改")
    print("=" * 80)
    
    # 设置参数
    excel_path = "../../../doc/附件1. 质控评分表v2.0.xlsx"
    model_config = deepseek_v3_config
    
    # 清理可能存在的测试目录
    test_json_dir = "Medical_Record_List_Json"
    if os.path.exists(test_json_dir):
        shutil.rmtree(test_json_dir)
        print(f"🧹 清理已存在的测试目录: {test_json_dir}")
    
    # 创建生成器实例
    generator = MedicalRecordListGenerator()
    
    print(f"\n📂 当前工作目录: {os.getcwd()}")
    print(f"📁 预期JSON输出目录: {os.path.abspath(test_json_dir)}")
    
    # 执行基础步骤
    print("\n" + "=" * 60)
    print("Step 1-3: 执行基础步骤")
    print("=" * 60)
    
    if not generator.read_excel_file(excel_path):
        print("❌ Excel文件读取失败")
        return False
    
    if not generator.validate_and_extract_data():
        print("❌ 数据验证和提取失败")
        return False
    
    if not generator.generate_category_statistics():
        print("❌ 类别统计生成失败")
        return False
    
    print("✅ 基础步骤完成")
    
    # 创建简单的翻译数据用于测试
    print("\n" + "=" * 60)
    print("Step 4: 创建测试翻译数据")
    print("=" * 60)
    
    # 创建少量翻译数据用于测试
    test_translations = {}
    for category, stats in generator.category_stats.items():
        # 只取前2个值
        test_values = stats['unique_values'][:2]
        category_translations = {}
        for chinese_term in test_values:
            # 简单的测试翻译
            english_term = f"Test_{chinese_term.replace(' ', '_')}"
            category_translations[chinese_term] = english_term
        test_translations[category] = category_translations
    
    generator.translations = test_translations
    print(f"✅ 创建了 {len(test_translations)} 个类别的测试翻译数据")
    
    # 测试JSON文件生成
    print("\n" + "=" * 60)
    print("Step 5: 测试JSON文件生成")
    print("=" * 60)
    
    print("🔧 测试默认输出目录...")
    success = generator.generate_json_files()  # 使用默认参数
    
    if success:
        print("✅ JSON文件生成成功")
        
        # 检查目录是否创建
        if os.path.exists(test_json_dir):
            print(f"✅ JSON输出目录已创建: {os.path.abspath(test_json_dir)}")
            
            # 检查生成的文件
            json_files = [f for f in os.listdir(test_json_dir) if f.endswith('.json')]
            print(f"✅ 生成了 {len(json_files)} 个JSON文件")
            
            # 显示前5个文件
            print("📄 生成的JSON文件示例:")
            for i, filename in enumerate(json_files[:5], 1):
                file_path = os.path.join(test_json_dir, filename)
                file_size = os.path.getsize(file_path)
                print(f"   {i}. {filename} ({file_size} bytes)")
            
            if len(json_files) > 5:
                print(f"   ... 还有 {len(json_files) - 5} 个文件")
        else:
            print("❌ JSON输出目录未创建")
            return False
    else:
        print("❌ JSON文件生成失败")
        return False
    
    # 测试Excel和Markdown文件仍在当前目录
    print("\n" + "=" * 60)
    print("Step 6-7: 测试Excel和Markdown文件输出")
    print("=" * 60)
    
    current_dir = "."
    
    print("🔧 测试Excel统计表生成...")
    if generator.generate_excel_statistics(current_dir):
        excel_files = [f for f in os.listdir(current_dir) if f.startswith('category_translations_') and f.endswith('.xlsx')]
        if excel_files:
            print(f"✅ Excel文件生成到当前目录: {excel_files[-1]}")
        else:
            print("⚠️ Excel文件未找到")
    
    print("🔧 测试Markdown统计表生成...")
    if generator.generate_markdown_statistics(current_dir):
        md_files = [f for f in os.listdir(current_dir) if f.startswith('category_translations_') and f.endswith('.md')]
        if md_files:
            print(f"✅ Markdown文件生成到当前目录: {md_files[-1]}")
        else:
            print("⚠️ Markdown文件未找到")
    
    # 验证路径分离
    print("\n" + "=" * 60)
    print("路径分离验证")
    print("=" * 60)
    
    json_in_current = [f for f in os.listdir(current_dir) if f.endswith('.json')]
    excel_in_json_dir = []
    md_in_json_dir = []
    
    if os.path.exists(test_json_dir):
        excel_in_json_dir = [f for f in os.listdir(test_json_dir) if f.endswith('.xlsx')]
        md_in_json_dir = [f for f in os.listdir(test_json_dir) if f.endswith('.md')]
    
    print(f"📊 验证结果:")
    print(f"   当前目录中的JSON文件: {len(json_in_current)} 个 {'✅' if len(json_in_current) == 0 else '❌'}")
    print(f"   JSON目录中的Excel文件: {len(excel_in_json_dir)} 个 {'✅' if len(excel_in_json_dir) == 0 else '❌'}")
    print(f"   JSON目录中的Markdown文件: {len(md_in_json_dir)} 个 {'✅' if len(md_in_json_dir) == 0 else '❌'}")
    
    # 总结
    print("\n" + "=" * 80)
    print("🎉 测试完成！")
    print("=" * 80)
    
    success_criteria = [
        os.path.exists(test_json_dir),
        len(json_files) > 0,
        len(json_in_current) == 0,
        len(excel_in_json_dir) == 0,
        len(md_in_json_dir) == 0
    ]
    
    all_success = all(success_criteria)
    
    print("📋 测试结果:")
    print(f"   ✅ JSON目录创建: {'通过' if success_criteria[0] else '失败'}")
    print(f"   ✅ JSON文件生成: {'通过' if success_criteria[1] else '失败'}")
    print(f"   ✅ JSON文件路径分离: {'通过' if success_criteria[2] else '失败'}")
    print(f"   ✅ Excel文件路径正确: {'通过' if success_criteria[3] else '失败'}")
    print(f"   ✅ Markdown文件路径正确: {'通过' if success_criteria[4] else '失败'}")
    
    print(f"\n🎯 总体结果: {'✅ 全部通过' if all_success else '❌ 部分失败'}")
    
    if all_success:
        print("\n🎉 JSON文件输出路径修改成功！")
        print("   - JSON文件现在输出到 Medical_Record_List_Json 目录")
        print("   - Excel和Markdown文件仍输出到当前目录")
        print("   - 路径分离功能正常工作")
    
    return all_success

if __name__ == "__main__":
    test_json_output_path()
