#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强分类器测试脚本
测试新的医疗记录质量控制分类逻辑
"""

import sys
import os

# 添加父目录到路径以导入核心模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from rule_type_classifier import classify_rule_type, _get_default_classification
from medical_document_standards import (
    classify_rule_by_standards,
    normalize_document_type,
    get_required_sections,
    is_structural_issue,
    is_content_issue
)

def test_document_type_normalization():
    """测试文档类型标准化"""
    print("\n" + "=" * 60)
    print("测试1: 文档类型标准化")
    print("=" * 60)
    
    test_cases = [
        ("首次病程", "首次病程记录"),
        ("首次病程记录", "首次病程记录"),
        ("入院记录", "入院记录"),
        ("入院病历", "入院记录"),
        ("出院记录", "出院记录"),
        ("出院小结", "出院记录")
    ]
    
    for input_type, expected in test_cases:
        result = normalize_document_type(input_type)
        status = "✅" if result == expected else "❌"
        print(f"{status} {input_type} -> {result} (期望: {expected})")

def test_required_sections():
    """测试必需章节获取"""
    print("\n" + "=" * 60)
    print("测试2: 必需章节获取")
    print("=" * 60)
    
    test_docs = ["首次病程记录", "入院记录", "出院记录"]
    
    for doc_type in test_docs:
        sections = get_required_sections(doc_type)
        print(f"\n📋 {doc_type}:")
        for section in sections:
            print(f"  - {section}")

def test_structural_vs_content_detection():
    """测试结构性问题与内容性问题检测"""
    print("\n" + "=" * 60)
    print("测试3: 结构性问题 vs 内容性问题检测")
    print("=" * 60)
    
    test_cases = [
        # 结构性问题（应该返回True）
        {
            "rule_content": "首次病程缺病例特点",
            "rule_type": "段落完整性",
            "expected_structural": True,
            "description": "整个章节缺失"
        },
        {
            "rule_content": "缺主诉",
            "rule_type": "段落完整性", 
            "expected_structural": True,
            "description": "标准章节缺失"
        },
        {
            "rule_content": "入院记录未在24小时内完成",
            "rule_type": "时效性",
            "expected_structural": True,
            "description": "时效性问题"
        },
        # 内容性问题（应该返回False）
        {
            "rule_content": "主诉描述有缺陷",
            "rule_type": "内容完整性",
            "expected_structural": False,
            "description": "内容质量问题"
        },
        {
            "rule_content": "现病史缺发病情况",
            "rule_type": "内容完整性",
            "expected_structural": False,
            "description": "章节内容不充分"
        },
        {
            "rule_content": "现病史缺主要症状特点及其发展变化情况",
            "rule_type": "内容完整性",
            "expected_structural": False,
            "description": "具体内容要素缺失"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        is_structural = is_structural_issue(case["rule_content"], case["rule_type"])
        is_content = is_content_issue(case["rule_content"], case["rule_type"])
        
        structural_status = "✅" if is_structural == case["expected_structural"] else "❌"
        content_status = "✅" if is_content != case["expected_structural"] else "❌"
        
        print(f"\n{i}. {case['description']}")
        print(f"   规则内容: {case['rule_content']}")
        print(f"   {structural_status} 结构性问题: {is_structural} (期望: {case['expected_structural']})")
        print(f"   {content_status} 内容性问题: {is_content}")

def test_standard_classification():
    """测试标准化分类逻辑"""
    print("\n" + "=" * 60)
    print("测试4: 标准化分类逻辑")
    print("=" * 60)
    
    test_rules = [
        # 规则质控案例
        {
            "rule_content": "首次病程缺病例特点",
            "rule_type_chinese": "段落完整性",
            "classification_chinese": "手术科室",
            "document_type_chinese": "首次病程记录",
            "belonging_project_chinese": "病程记录",
            "expected": "规则",
            "reason": "整个章节缺失"
        },
        {
            "rule_content": "入院记录未在24小时内完成",
            "rule_type_chinese": "时效性",
            "classification_chinese": "手术科室", 
            "document_type_chinese": "入院记录",
            "belonging_project_chinese": "入院记录",
            "expected": "规则",
            "reason": "时效性问题"
        },
        {
            "rule_content": "缺主诉",
            "rule_type_chinese": "段落完整性",
            "classification_chinese": "手术科室",
            "document_type_chinese": "入院记录",
            "belonging_project_chinese": "入院记录",
            "expected": "规则",
            "reason": "标准章节缺失"
        },
        # 内涵质控案例
        {
            "rule_content": "主诉描述有缺陷",
            "rule_type_chinese": "内容完整性",
            "classification_chinese": "手术科室",
            "document_type_chinese": "入院记录",
            "belonging_project_chinese": "入院记录",
            "expected": "内涵",
            "reason": "内容质量问题"
        },
        {
            "rule_content": "现病史缺发病情况",
            "rule_type_chinese": "内容完整性",
            "classification_chinese": "手术科室",
            "document_type_chinese": "入院记录",
            "belonging_project_chinese": "入院记录",
            "expected": "内涵",
            "reason": "章节内容不充分"
        },
        {
            "rule_content": "现病史主要症状或体征、症状性质、持续时间与主诉不符",
            "rule_type_chinese": "数据一致性",
            "classification_chinese": "手术科室",
            "document_type_chinese": "入院记录",
            "belonging_project_chinese": "入院记录",
            "expected": "内涵",
            "reason": "数据一致性问题"
        }
    ]
    
    correct_count = 0
    total_count = len(test_rules)
    
    for i, rule in enumerate(test_rules, 1):
        result = classify_rule_by_standards(rule)
        is_correct = result == rule["expected"]
        status = "✅" if is_correct else "❌"
        
        if is_correct:
            correct_count += 1
            
        print(f"\n{i}. {rule['reason']}")
        print(f"   规则内容: {rule['rule_content']}")
        print(f"   {status} 分类结果: {result} (期望: {rule['expected']})")
    
    accuracy = correct_count / total_count * 100
    print(f"\n📊 分类准确率: {correct_count}/{total_count} ({accuracy:.1f}%)")

def test_enhanced_classifier():
    """测试增强的分类器"""
    print("\n" + "=" * 60)
    print("测试5: 增强分类器（不使用LLM）")
    print("=" * 60)
    
    # 使用与测试4相同的测试用例
    test_rules = [
        {
            "rule_content": "首次病程缺病例特点",
            "rule_type_chinese": "段落完整性",
            "classification_chinese": "手术科室",
            "document_type_chinese": "首次病程记录",
            "belonging_project_chinese": "病程记录",
            "expected": "规则"
        },
        {
            "rule_content": "主诉描述有缺陷",
            "rule_type_chinese": "内容完整性",
            "classification_chinese": "手术科室",
            "document_type_chinese": "入院记录",
            "belonging_project_chinese": "入院记录",
            "expected": "内涵"
        }
    ]
    
    for i, rule in enumerate(test_rules, 1):
        # 测试默认分类（不使用LLM）
        result = _get_default_classification(rule)
        status = "✅" if result == rule["expected"] else "❌"
        
        print(f"{i}. {status} {rule['rule_content'][:30]}... -> {result} (期望: {rule['expected']})")

def main():
    """主测试函数"""
    print("🔬 医疗记录质量控制分类逻辑测试")
    print("=" * 60)
    
    try:
        test_document_type_normalization()
        test_required_sections()
        test_structural_vs_content_detection()
        test_standard_classification()
        test_enhanced_classifier()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
