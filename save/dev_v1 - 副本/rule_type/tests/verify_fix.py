#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复效果的脚本
检查问题规则是否被正确分类
"""

import sys
import os
import json

# 添加父目录到路径以导入核心模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from medical_document_standards import classify_rule_by_standards

def verify_specific_rule():
    """验证特定问题规则的分类"""
    print("🔍 验证问题规则分类修复")
    print("=" * 60)
    
    # 问题规则
    problem_rule = {
        "rule_content": "首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断",
        "rule_type_chinese": "段落完整性",
        "document_type_chinese": "首次病程记录",
        "belonging_project_chinese": "病程记录"
    }
    
    print(f"📋 问题规则: {problem_rule['rule_content']}")
    print(f"📋 规则类型: {problem_rule['rule_type_chinese']}")
    print(f"📋 文档类型: {problem_rule['document_type_chinese']}")
    
    # 执行分类
    classification = classify_rule_by_standards(problem_rule)
    
    # 显示结果
    if classification == "规则":
        print("✅ 修复成功！规则被正确分类为：规则质控")
        print("💡 分类依据：虽然有条件限制，但核心是整个鉴别诊断章节的缺失，属于结构完整性问题")
        return True
    else:
        print(f"❌ 修复失败！规则被错误分类为：{classification}质控")
        return False

def process_initial_progress_note():
    """处理首次病程记录文件"""
    print(f"\n" + "=" * 60)
    print("📁 处理首次病程记录文件")
    print("=" * 60)
    
    # 文件路径
    input_file = os.path.join(current_dir, "..", "Medical_Record_List", "Medical_Record_List_Json", "Initial_Progress_Note.json")
    output_file = os.path.join(current_dir, "rule_type_json_fixed", "Initial_Progress_Note_fixed.json")
    
    try:
        # 读取输入文件
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📋 文档类型: {data['metadata']['document_type_chinese']}")
        print(f"📋 规则总数: {data['metadata']['total_records']}")
        
        # 处理每条规则
        processed_records = []
        classification_stats = {"规则": 0, "内涵": 0}
        problem_rule_found = False
        
        for record in data['records']:
            # 执行分类
            classification = classify_rule_by_standards(record)
            
            # 添加分类结果
            record_with_type = record.copy()
            record_with_type['type'] = classification
            processed_records.append(record_with_type)
            
            # 统计
            classification_stats[classification] += 1
            
            # 检查是否是问题规则
            if "首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断" in record.get('rule_content', ''):
                problem_rule_found = True
                if classification == "规则":
                    print(f"✅ 问题规则已修复：{record['rule_content'][:50]}... → 规则质控")
                else:
                    print(f"❌ 问题规则仍未修复：{record['rule_content'][:50]}... → {classification}质控")
        
        # 更新元数据
        data['metadata']['classification_stats'] = classification_stats
        data['metadata']['processing_time'] = "2025-08-04T16:15:00"
        data['metadata']['version'] = "2.0_fixed"
        data['records'] = processed_records
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 保存结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        # 显示统计
        print(f"\n📊 分类统计:")
        print(f"  规则质控: {classification_stats['规则']} 条")
        print(f"  内涵质控: {classification_stats['内涵']} 条")
        print(f"  总计: {sum(classification_stats.values())} 条")
        
        print(f"\n💾 输出文件: {output_file}")
        
        if not problem_rule_found:
            print("⚠️  未找到问题规则，可能规则内容有变化")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理文件时发生错误: {e}")
        return False

def compare_before_after():
    """对比修复前后的分类结果"""
    print(f"\n" + "=" * 60)
    print("📊 修复前后对比")
    print("=" * 60)
    
    try:
        # 读取修复前的文件
        old_file = "rule_type_json/Initial_Progress_Note_type.json"
        if os.path.exists(old_file):
            with open(old_file, 'r', encoding='utf-8') as f:
                old_data = json.load(f)
            old_stats = old_data['metadata'].get('classification_stats', {})
            print(f"修复前统计:")
            print(f"  规则质控: {old_stats.get('规则', 0)} 条")
            print(f"  内涵质控: {old_stats.get('内涵', 0)} 条")
        
        # 读取修复后的文件
        new_file = "rule_type_json_fixed/Initial_Progress_Note_fixed.json"
        if os.path.exists(new_file):
            with open(new_file, 'r', encoding='utf-8') as f:
                new_data = json.load(f)
            new_stats = new_data['metadata'].get('classification_stats', {})
            print(f"\n修复后统计:")
            print(f"  规则质控: {new_stats.get('规则', 0)} 条")
            print(f"  内涵质控: {new_stats.get('内涵', 0)} 条")
            
            # 计算变化
            rule_change = new_stats.get('规则', 0) - old_stats.get('规则', 0)
            content_change = new_stats.get('内涵', 0) - old_stats.get('内涵', 0)
            
            print(f"\n📈 变化:")
            print(f"  规则质控: {rule_change:+d} 条")
            print(f"  内涵质控: {content_change:+d} 条")
            
    except Exception as e:
        print(f"❌ 对比时发生错误: {e}")

def main():
    """主函数"""
    try:
        # 验证特定规则
        success = verify_specific_rule()
        
        # 处理完整文件
        if success:
            process_success = process_initial_progress_note()
            if process_success:
                compare_before_after()
        
        print(f"\n" + "=" * 60)
        if success:
            print("✅ 问题规则分类修复验证成功！")
        else:
            print("❌ 问题规则分类修复验证失败！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
