# -*- coding: utf-8 -*-
"""
快速API测试脚本
简单测试Flask Web应用的基本功能
"""
import requests
import json

# API配置
API_BASE_URL = "http://localhost:9086"

def test_health():
    """测试健康检查"""
    print("🏥 测试健康检查...")
    try:
        response = requests.get(f"{API_BASE_URL}/hospical/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查通过: {data.get('status', '未知')}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_simple_quality_control():
    """测试简单质控请求"""
    print("\n🔍 测试简单质控...")
    
    # 简单的测试数据
    test_data = {
        "type": "Discharge Summary",
        "data": {
            "Patient": {
                "PatientId": "TEST001",
                "PatientName": "测试患者",
                "Age": 30,
                "SexName": "男"
            },
            "VisitInfo": {
                "AdmissionNumber": "A001",
                "DeptName": "内科"
            },
            "RepSummaryInfo": {
                "AssayResult": "血常规正常",
                "IsTumor": "否",
                "TreatmentOutcomes": "好转"
            }
        }
    }
    
    try:
        print("  发送质控请求...")
        response = requests.post(
            f"{API_BASE_URL}/hospical/quality_control",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=60  # 增加超时时间
        )
        
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success', False):
                data = result.get('data', {})
                quality_score = data.get('quality_score', {})
                print(f"  ✅ 质控成功")
                print(f"  🏆 质控分数: {quality_score.get('total_score', 0)}/100")
                print(f"  📊 质控状态: {data.get('quality_status', '未知')}")
                return True
            else:
                error = result.get('error', {})
                print(f"  ❌ 质控失败: {error.get('message', '未知错误')}")
                return False
        else:
            print(f"  ❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ 请求异常: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🚨 测试错误处理...")
    
    # 测试无效类型
    invalid_data = {
        "type": "Invalid Type",
        "data": {}
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/hospical/quality_control",
            json=invalid_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 400:
            result = response.json()
            error = result.get('error', {})
            print(f"  ✅ 错误处理正确: {error.get('message', '未知错误')}")
            return True
        else:
            print(f"  ❌ 错误处理异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("Flask API 快速测试")
    print("=" * 50)
    
    # 测试健康检查
    if not test_health():
        print("\n❌ API服务未启动，请先运行:")
        print("python quality_control_api.py")
        return
    
    # 测试质控功能
    test_simple_quality_control()
    
    # 测试错误处理
    test_error_handling()
    
    print("\n" + "=" * 50)
    print("快速测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
