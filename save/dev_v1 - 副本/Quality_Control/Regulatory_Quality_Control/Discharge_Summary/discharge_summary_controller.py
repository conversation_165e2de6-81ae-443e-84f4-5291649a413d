# -*- coding: utf-8 -*-
"""
Discharge Summary 规则质控子控制器
负责协调该文档类型下的所有规则质控检查
"""
import sys
from pathlib import Path

# 导入所有规则检查函数
from .rule_f071c274 import check_rule
from .rule_f071c274 import check_rule as rule_f071c274_check
from .rule_b24ccb01 import check_rule
from .rule_b24ccb01 import check_rule as rule_b24ccb01_check
from .rule_86604b78 import check_rule
from .rule_86604b78 import check_rule as rule_86604b78_check
from .rule_a2f76422 import check_rule
from .rule_a2f76422 import check_rule as rule_a2f76422_check
from .rule_3fc8b2c8 import check_rule
from .rule_3fc8b2c8 import check_rule as rule_3fc8b2c8_check
from .rule_17ed484b import check_rule
from .rule_17ed484b import check_rule as rule_17ed484b_check
from .rule_c852e6e2 import check_rule
from .rule_c852e6e2 import check_rule as rule_c852e6e2_check
from .rule_5a88b5fc import check_rule
from .rule_5a88b5fc import check_rule as rule_5a88b5fc_check
from .rule_063a8079 import check_rule
from .rule_063a8079 import check_rule as rule_063a8079_check
from .rule_195eff15 import check_rule
from .rule_195eff15 import check_rule as rule_195eff15_check
from .rule_241f9e5e import check_rule
from .rule_241f9e5e import check_rule as rule_241f9e5e_check
from .rule_236364de import check_rule
from .rule_236364de import check_rule as rule_236364de_check
from .rule_b3bf1f1e import check_rule
from .rule_b3bf1f1e import check_rule as rule_b3bf1f1e_check
from .rule_b240acc0 import check_rule
from .rule_b240acc0 import check_rule as rule_b240acc0_check
from .rule_e8546f3c import check_rule
from .rule_e8546f3c import check_rule as rule_e8546f3c_check
from .rule_0c7d2a0c import check_rule
from .rule_0c7d2a0c import check_rule as rule_0c7d2a0c_check
from .rule_3db8d2ec import check_rule
from .rule_3db8d2ec import check_rule as rule_3db8d2ec_check
from .rule_d9ae43d6 import check_rule
from .rule_d9ae43d6 import check_rule as rule_d9ae43d6_check
from .rule_a1df36c3 import check_rule
from .rule_a1df36c3 import check_rule as rule_a1df36c3_check
from .rule_4542640f import check_rule
from .rule_4542640f import check_rule as rule_4542640f_check
from .rule_4084bb5a import check_rule
from .rule_4084bb5a import check_rule as rule_4084bb5a_check
from .rule_fb10c46d import check_rule
from .rule_fb10c46d import check_rule as rule_fb10c46d_check

class DischargeSummaryRegulatoryController:
    """
    Discharge Summary 规则质控控制器
    """

    def __init__(self):
        self.document_type = "Discharge Summary"
        self.total_rules = 22

    def run_regulatory_quality_control(self, medical_record):
        """
        运行所有规则质控检查

        Args:
            medical_record (dict): 病历数据

        Returns:
            dict: 规则质控结果
        """
        results = {}

        print(f"开始执行 {self.document_type} 规则质控，共 {self.total_rules} 条规则...")

        # 执行规则: 缺出院记录，或未在出院后24小时内完成
        try:
            result = rule_f071c274_check(medical_record)
            results['缺出院记录，或未在出院后24小时内完成'] = {
                'rule_id': 'rule_f071c274',
                'rule_type': '时效性',
                'classification': '手术科室',
                'deduction_points': 60.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_f071c274 执行失败: {e}')
            results['缺出院记录，或未在出院后24小时内完成'] = {
                'rule_id': 'rule_f071c274',
                'rule_type': '时效性',
                'classification': '手术科室',
                'deduction_points': 60.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺诊疗经过
        try:
            result = rule_b24ccb01_check(medical_record)
            results['缺诊疗经过'] = {
                'rule_id': 'rule_b24ccb01',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_b24ccb01 执行失败: {e}')
            results['缺诊疗经过'] = {
                'rule_id': 'rule_b24ccb01',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺入院诊断
        try:
            result = rule_86604b78_check(medical_record)
            results['缺入院诊断'] = {
                'rule_id': 'rule_86604b78',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_86604b78 执行失败: {e}')
            results['缺入院诊断'] = {
                'rule_id': 'rule_86604b78',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺入院情况
        try:
            result = rule_a2f76422_check(medical_record)
            results['缺入院情况'] = {
                'rule_id': 'rule_a2f76422',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_a2f76422 执行失败: {e}')
            results['缺入院情况'] = {
                'rule_id': 'rule_a2f76422',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺出院前一天或当天上级医生同意同意出院记录
        try:
            result = rule_3fc8b2c8_check(medical_record)
            results['缺出院前一天或当天上级医生同意同意出院记录'] = {
                'rule_id': 'rule_3fc8b2c8',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 3.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_3fc8b2c8 执行失败: {e}')
            results['缺出院前一天或当天上级医生同意同意出院记录'] = {
                'rule_id': 'rule_3fc8b2c8',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 3.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺出院诊断
        try:
            result = rule_17ed484b_check(medical_record)
            results['缺出院诊断'] = {
                'rule_id': 'rule_17ed484b',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_17ed484b 执行失败: {e}')
            results['缺出院诊断'] = {
                'rule_id': 'rule_17ed484b',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺出院情况
        try:
            result = rule_c852e6e2_check(medical_record)
            results['缺出院情况'] = {
                'rule_id': 'rule_c852e6e2',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_c852e6e2 执行失败: {e}')
            results['缺出院情况'] = {
                'rule_id': 'rule_c852e6e2',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺出院日期
        try:
            result = rule_5a88b5fc_check(medical_record)
            results['缺出院日期'] = {
                'rule_id': 'rule_5a88b5fc',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_5a88b5fc 执行失败: {e}')
            results['缺出院日期'] = {
                'rule_id': 'rule_5a88b5fc',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺入院日期
        try:
            result = rule_063a8079_check(medical_record)
            results['缺入院日期'] = {
                'rule_id': 'rule_063a8079',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_063a8079 执行失败: {e}')
            results['缺入院日期'] = {
                'rule_id': 'rule_063a8079',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等
        try:
            result = rule_195eff15_check(medical_record)
            results['缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等'] = {
                'rule_id': 'rule_195eff15',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 30.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_195eff15 执行失败: {e}')
            results['缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等'] = {
                'rule_id': 'rule_195eff15',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 30.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺住院医师、主治医师或以上签名
        try:
            result = rule_241f9e5e_check(medical_record)
            results['缺住院医师、主治医师或以上签名'] = {
                'rule_id': 'rule_241f9e5e',
                'rule_type': '签名合理性',
                'classification': '手术科室',
                'deduction_points': 3.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_241f9e5e 执行失败: {e}')
            results['缺住院医师、主治医师或以上签名'] = {
                'rule_id': 'rule_241f9e5e',
                'rule_type': '签名合理性',
                'classification': '手术科室',
                'deduction_points': 3.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺出院记录，或未在出院后24小时内完成
        try:
            result = rule_236364de_check(medical_record)
            results['缺出院记录，或未在出院后24小时内完成'] = {
                'rule_id': 'rule_236364de',
                'rule_type': '时效性',
                'classification': '非手术科室',
                'deduction_points': 60.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_236364de 执行失败: {e}')
            results['缺出院记录，或未在出院后24小时内完成'] = {
                'rule_id': 'rule_236364de',
                'rule_type': '时效性',
                'classification': '非手术科室',
                'deduction_points': 60.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺诊疗经过
        try:
            result = rule_b3bf1f1e_check(medical_record)
            results['缺诊疗经过'] = {
                'rule_id': 'rule_b3bf1f1e',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_b3bf1f1e 执行失败: {e}')
            results['缺诊疗经过'] = {
                'rule_id': 'rule_b3bf1f1e',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺入院诊断
        try:
            result = rule_b240acc0_check(medical_record)
            results['缺入院诊断'] = {
                'rule_id': 'rule_b240acc0',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_b240acc0 执行失败: {e}')
            results['缺入院诊断'] = {
                'rule_id': 'rule_b240acc0',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺入院情况
        try:
            result = rule_e8546f3c_check(medical_record)
            results['缺入院情况'] = {
                'rule_id': 'rule_e8546f3c',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_e8546f3c 执行失败: {e}')
            results['缺入院情况'] = {
                'rule_id': 'rule_e8546f3c',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺出院前一天或当天上级医生同意同意出院记录
        try:
            result = rule_0c7d2a0c_check(medical_record)
            results['缺出院前一天或当天上级医生同意同意出院记录'] = {
                'rule_id': 'rule_0c7d2a0c',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 3.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_0c7d2a0c 执行失败: {e}')
            results['缺出院前一天或当天上级医生同意同意出院记录'] = {
                'rule_id': 'rule_0c7d2a0c',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 3.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺出院诊断
        try:
            result = rule_3db8d2ec_check(medical_record)
            results['缺出院诊断'] = {
                'rule_id': 'rule_3db8d2ec',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_3db8d2ec 执行失败: {e}')
            results['缺出院诊断'] = {
                'rule_id': 'rule_3db8d2ec',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺出院情况
        try:
            result = rule_d9ae43d6_check(medical_record)
            results['缺出院情况'] = {
                'rule_id': 'rule_d9ae43d6',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_d9ae43d6 执行失败: {e}')
            results['缺出院情况'] = {
                'rule_id': 'rule_d9ae43d6',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺出院日期
        try:
            result = rule_a1df36c3_check(medical_record)
            results['缺出院日期'] = {
                'rule_id': 'rule_a1df36c3',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_a1df36c3 执行失败: {e}')
            results['缺出院日期'] = {
                'rule_id': 'rule_a1df36c3',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺入院日期
        try:
            result = rule_4542640f_check(medical_record)
            results['缺入院日期'] = {
                'rule_id': 'rule_4542640f',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_4542640f 执行失败: {e}')
            results['缺入院日期'] = {
                'rule_id': 'rule_4542640f',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等
        try:
            result = rule_4084bb5a_check(medical_record)
            results['缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等'] = {
                'rule_id': 'rule_4084bb5a',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 30.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_4084bb5a 执行失败: {e}')
            results['缺出院医嘱，出院带药未写明药名、剂量、用法、带药总量及随访要求和注意事项交待等'] = {
                'rule_id': 'rule_4084bb5a',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 30.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 缺住院医师、主治医师或以上签名
        try:
            result = rule_fb10c46d_check(medical_record)
            results['缺住院医师、主治医师或以上签名'] = {
                'rule_id': 'rule_fb10c46d',
                'rule_type': '签名合理性',
                'classification': '非手术科室',
                'deduction_points': 3.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_fb10c46d 执行失败: {e}')
            results['缺住院医师、主治医师或以上签名'] = {
                'rule_id': 'rule_fb10c46d',
                'rule_type': '签名合理性',
                'classification': '非手术科室',
                'deduction_points': 3.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }


        print(f"{self.document_type} 规则质控执行完成，共检查 {len(results)} 条规则")
        return results

    def get_summary(self, results):
        """
        获取质控结果摘要

        Args:
            results (dict): 质控结果

        Returns:
            dict: 摘要信息
        """
        total_rules = len(results)
        problem_rules = sum(1 for r in results.values() if r.get('has_problem', False))
        total_deduction = sum(r.get('deduction_points', 0) for r in results.values() if r.get('has_problem', False))

        return {
            'document_type': self.document_type,
            'total_rules': total_rules,
            'problem_rules': problem_rules,
            'total_deduction_points': total_deduction,
            'compliance_rate': (total_rules - problem_rules) / total_rules * 100 if total_rules > 0 else 0
        }

# 兼容性函数，供主控制器调用
def run_regulatory_quality_control(medical_record):
    """兼容性函数"""
    controller = DischargeSummaryRegulatoryController()
    return controller.run_regulatory_quality_control(medical_record)

if __name__ == "__main__":
    # 测试示例
    controller = DischargeSummaryRegulatoryController()

    test_record = {
        "content": "测试Discharge Summary内容",
        "patient_info": {},
        "diagnosis": {},
        "treatment": {}
    }

    results = controller.run_regulatory_quality_control(test_record)
    summary = controller.get_summary(results)

    print("\n规则质控结果摘要:")
    print(f"文档类型: {summary['document_type']}")
    print(f"总规则数: {summary['total_rules']}")
    print(f"问题规则数: {summary['problem_rules']}")
    print(f"总扣分: {summary['total_deduction_points']}")
    print(f"合规率: {summary['compliance_rate']:.2f}%")
