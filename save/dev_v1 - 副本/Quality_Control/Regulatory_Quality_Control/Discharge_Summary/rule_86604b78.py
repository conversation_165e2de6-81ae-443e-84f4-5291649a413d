"""
Rule ID: rule_86604b78
Description: 段落完整性检查 - 缺入院诊断 (扣分: 6.0)
"""

def check_rule(medical_record):
    """Check if admission diagnosis section is missing in medical record.
    
    Args:
        medical_record (dict): 医疗记录数据字典
        
    Returns:
        bool: True表示存在问题（缺少入院诊断），False表示符合要求
    """
    try:
        # 检查是否缺少入院诊断字段或字段为空
        if 'admission_diagnosis' not in medical_record:
            return True
            
        value = medical_record['admission_diagnosis']
        if value is None or value == '':
            return True
            
        return False
            
    except Exception as e:
        # 捕获并处理所有异常情况
        print(f"Error checking rule: {e}")
        return False

if __name__ == "__main__":
    # 测试用例1: 缺失字段
    test1 = {}
    print("Test 1:", check_rule(test1))  # 预期输出: True
    
    # 测试用例2: 字段存在但为空字符串
    test2 = {'admission_diagnosis': ''}
    print("Test 2:", check_rule(test2))  # 预期输出: True
    
    # 测试用例3: 字段存在且有值
    test3 = {'admission_diagnosis': '急性支气管炎'}
    print("Test 3:", check_rule(test3))  # 预期输出: False
    
    # 测试用例4: 字段存在但为None
    test4 = {'admission_diagnosis': None}
    print("Test 4:", check_rule(test4))  # 预期输出: True