# 规则ID: rule_3d8e749b
# 描述: 首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断

import re

def check_rule(medical_record):
    """
    检查首次病程记录是否缺少鉴别诊断且诊断中不包含待查类
    返回True表示存在质量问题，False表示符合要求
    """
    try:
        # 检查必要字段是否存在
        if 'first_course' not in medical_record or 'diagnosis' not in medical_record:
            return False
            
        first_course = str(medical_record['first_course'])
        diagnosis = medical_record['diagnosis']
        
        # 检查是否存在待查类诊断
        has_pending = any(re.search(r'待查|待排|待诊断', d, re.IGNORECASE) for d in diagnosis)
        
        # 如果有待查类诊断直接返回False
        if has_pending:
            return False
            
        # 检查首次病程是否缺少鉴别诊断
        # 这里使用正则表达式匹配常见鉴别诊断关键词
        if not re.search(r'鉴别诊断|鉴别诊断：|鉴别诊断：|鉴别诊断：|鉴别诊断：', first_course):
            return True
            
        return False
            
    except Exception as e:
        # 捕获所有异常并返回False
        return False

if __name__ == '__main__':
    # 测试用例1: 正常情况
    test1 = {
        'first_course': '患者入院后完善检查，考虑肺炎，鉴别诊断包括支气管炎、肺结核等',
        'diagnosis': ['肺炎']
    }
    print("Test1:", check_rule(test1))  # 应返回False
    
    # 测试用例2: 缺鉴别诊断且无待查
    test2 = {
        'first_course': '患者入院后完善检查，考虑肺炎',
        'diagnosis': ['肺炎']
    }
    print("Test2:", check_rule(test2))  # 应返回True
    
    # 测试用例3: 缺鉴别诊断但有待查
    test3 = {
        'first_course': '患者入院后完善检查，考虑肺炎',
        'diagnosis': ['肺炎待查']
    }
    print("Test3:", check_rule(test3))  # 应返回False
    
    # 测试用例4: 包含鉴别诊断但有待查
    test4 = {
        'first_course': '患者入院后完善检查，考虑肺炎，需与支气管炎鉴别',
        'diagnosis': ['肺炎待查']
    }
    print("Test4:", check_rule(test4))  # 应返回False
    
    # 测试用例5: 缺少必要字段
    test5 = {
        'diagnosis': ['肺炎']
    }
    print("Test5:", check_rule(test5))  # 应返回False