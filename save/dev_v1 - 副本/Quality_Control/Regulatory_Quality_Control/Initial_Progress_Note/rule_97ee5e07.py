"""
Rule ID: rule_97ee5e07
Rule Description: 首次病程缺初步诊断
"""

def check_rule(medical_record):
    """
    检查首次病程记录是否缺少初步诊断字段
    返回True表示存在问题，False表示符合要求
    """
    try:
        # 获取首次病程记录部分
        initial_course = medical_record.get('initial_course', {})
        
        # 检查是否存在初步诊断字段且不为空
        if not isinstance(initial_course, dict):
            return True
            
        if 'preliminary_diagnosis' not in initial_course:
            return True
            
        if not str(initial_course['preliminary_diagnosis']).strip():
            return True
            
        return False
            
    except (AttributeError, KeyError, TypeError):
        # 处理数据结构异常情况
        return True

if __name__ == '__main__':
    # 测试用例1: 正常情况
    test1 = {
        'initial_course': {
            'preliminary_diagnosis': '疑似肺炎'
        }
    }
    assert check_rule(test1) == False
    
    # 测试用例2: 缺少诊断字段
    test2 = {
        'initial_course': {}
    }
    assert check_rule(test2) == True
    
    # 测试用例3: 诊断字段为空
    test3 = {
        'initial_course': {
            'preliminary_diagnosis': ''
        }
    }
    assert check_rule(test3) == True
    
    # 测试用例4: 数据结构异常
    test4 = 'invalid_data_type'
    assert check_rule(test4) == True
    
    # 测试用例5: 嵌套结构缺失
    test5 = {}
    assert check_rule(test5) == True
    
    print("所有测试用例通过")