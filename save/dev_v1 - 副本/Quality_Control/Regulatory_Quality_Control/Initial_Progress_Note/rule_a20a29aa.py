# 规则ID: rule_a20a29aa
# 描述: 首次病程未在患者入院后8小时内完成

import datetime

def check_rule(medical_record):
    """
    检查首次病程记录是否在入院8小时内完成
    返回True表示违反规则，False表示符合要求
    """
    try:
        # 获取入院时间和首次病程时间
        admission_time = medical_record['admission_time']
        first_course_time = medical_record['first_course_time']
        
        # 计算时间差
        time_diff = first_course_time - admission_time
        
        # 判断是否超过8小时
        if time_diff > datetime.timedelta(hours=8):
            return True
        return False
            
    except (KeyError, AttributeError) as e:
        # 处理时间字段缺失或非datetime类型的情况
        print(f"数据格式错误: {e}")
        return False
    except Exception as e:
        print(f"未知错误: {e}")
        return False

# 测试代码
if __name__ == "__main__":
    # 测试用例1: 正常情况(5小时内完成)
    test1 = {
        'admission_time': datetime.datetime(2023, 1, 1, 10, 0),
        'first_course_time': datetime.datetime(2023, 1, 1, 15, 0)
    }
    print("测试用例1结果:", check_rule(test1))  # 应返回False
    
    # 测试用例2: 违规情况(9小时后完成)
    test2 = {
        'admission_time': datetime.datetime(2023, 1, 1, 10, 0),
        'first_course_time': datetime.datetime(2023, 1, 1, 19, 0)
    }
    print("测试用例2结果:", check_rule(test2))  # 应返回True
    
    # 测试用例3: 缺失时间字段
    test3 = {
        'admission_time': datetime.datetime(2023, 1, 1, 10, 0)
    }
    print("测试用例3结果:", check_rule(test3))  # 应返回False
    
    # 测试用例4: 时间差正好8小时
    test4 = {
        'admission_time': datetime.datetime(2023, 1, 1, 10, 0),
        'first_course_time': datetime.datetime(2023, 1, 1, 18, 0)
    }
    print("测试用例4结果:", check_rule(test4))  # 应返回False