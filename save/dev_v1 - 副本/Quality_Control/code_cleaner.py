# -*- coding: utf-8 -*-
"""
质控代码清理工具
自动清理生成的Python文件中的Markdown标记和非代码文本
"""
import os
import re
import sys
from pathlib import Path
from typing import List, Tuple, Optional

class CodeCleaner:
    """代码清理器类"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.cleaned_files = []
        self.error_files = []
        
    def is_markdown_block_marker(self, line: str) -> bool:
        """检查是否为Markdown代码块标记"""
        line = line.strip()
        return line in ['```python', '```', '```py'] or line.startswith('```')
    
    def is_chinese_description(self, line: str) -> bool:
        """检查是否为中文描述文字（非代码注释）"""
        line = line.strip()
        
        # 空行不处理
        if not line:
            return False
            
        # Python注释保留
        if line.startswith('#'):
            return False
            
        # 文档字符串保留
        if line.startswith('"""') or line.startswith("'''"):
            return False
            
        # Python代码行保留
        python_keywords = [
            'import', 'from', 'def', 'class', 'if', 'else', 'elif', 'for', 
            'while', 'try', 'except', 'finally', 'with', 'return', 'yield',
            'raise', 'assert', 'break', 'continue', 'pass', 'global', 'nonlocal'
        ]
        
        # 检查是否以Python关键字开头
        first_word = line.split()[0] if line.split() else ""
        if first_word in python_keywords:
            return False
            
        # 检查是否为赋值语句
        if '=' in line and not line.startswith('='):
            return False
            
        # 检查是否为函数调用
        if '(' in line and ')' in line:
            return False
            
        # 检查常见的中文描述模式
        chinese_patterns = [
            r'^这个代码实现了',
            r'^这个函数',
            r'^主要功能',
            r'^使用方法',
            r'^注意事项',
            r'^代码说明',
            r'^功能描述',
            r'^\d+\.\s*[^#]',  # 数字列表（非注释）
            r'^[一二三四五六七八九十]+[、.]',  # 中文数字列表
        ]
        
        for pattern in chinese_patterns:
            if re.match(pattern, line):
                return True
                
        # 检查是否包含大量中文且不是注释
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', line))
        total_chars = len(line)
        
        # 如果中文字符占比超过50%且不是注释，可能是描述文字
        if total_chars > 10 and chinese_chars / total_chars > 0.5:
            # 但如果包含Python语法元素，则保留
            if any(char in line for char in ['(', ')', '[', ']', '{', '}', '=', ':']):
                return False
            return True
            
        return False
    
    def clean_code_content(self, content: str) -> str:
        """清理代码内容"""
        lines = content.split('\n')
        cleaned_lines = []
        in_docstring = False
        docstring_marker = None
        skip_description_block = False
        
        for i, line in enumerate(lines):
            original_line = line
            stripped_line = line.strip()
            
            # 检查文档字符串状态
            if '"""' in line or "'''" in line:
                if not in_docstring:
                    in_docstring = True
                    docstring_marker = '"""' if '"""' in line else "'''"
                    cleaned_lines.append(original_line)
                    continue
                elif docstring_marker in line:
                    in_docstring = False
                    docstring_marker = None
                    cleaned_lines.append(original_line)
                    continue
            
            # 在文档字符串内部，保留所有内容
            if in_docstring:
                cleaned_lines.append(original_line)
                continue
            
            # 移除Markdown代码块标记
            if self.is_markdown_block_marker(line):
                continue
                
            # 检查是否为描述性文字块的开始
            if self.is_chinese_description(line):
                # 检查后续几行是否也是描述文字
                description_block_size = 0
                for j in range(i, min(i + 5, len(lines))):
                    if self.is_chinese_description(lines[j]):
                        description_block_size += 1
                    else:
                        break
                
                # 如果连续多行都是描述文字，跳过整个块
                if description_block_size >= 2:
                    skip_description_block = True
                    continue
                else:
                    # 单行描述文字也跳过
                    continue
            
            # 如果在跳过描述块的状态中
            if skip_description_block:
                if not self.is_chinese_description(line) and stripped_line:
                    skip_description_block = False
                    cleaned_lines.append(original_line)
                continue
            
            # 保留其他所有行
            cleaned_lines.append(original_line)
        
        # 清理多余的空行
        result_lines = []
        prev_empty = False
        
        for line in cleaned_lines:
            is_empty = not line.strip()
            
            # 避免连续多个空行
            if is_empty and prev_empty:
                continue
                
            result_lines.append(line)
            prev_empty = is_empty
        
        # 移除开头和结尾的空行
        while result_lines and not result_lines[0].strip():
            result_lines.pop(0)
        while result_lines and not result_lines[-1].strip():
            result_lines.pop()
            
        return '\n'.join(result_lines)
    
    def clean_file(self, file_path: Path) -> bool:
        """清理单个文件"""
        try:
            # 读取原文件
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # 清理内容
            cleaned_content = self.clean_code_content(original_content)
            
            # 检查是否有变化
            if cleaned_content != original_content:
                # 备份原文件
                backup_path = file_path.with_suffix(file_path.suffix + '.backup')
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                
                # 写入清理后的内容
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(cleaned_content)
                
                self.cleaned_files.append(str(file_path))
                print(f"✓ 已清理: {file_path}")
                print(f"  备份文件: {backup_path}")
                return True
            else:
                print(f"- 无需清理: {file_path}")
                return False
                
        except Exception as e:
            self.error_files.append((str(file_path), str(e)))
            print(f"✗ 清理失败: {file_path} - {e}")
            return False
    
    def clean_directory(self, directory: Path, recursive: bool = True) -> None:
        """清理目录中的所有Python文件"""
        if not directory.exists():
            print(f"目录不存在: {directory}")
            return
        
        pattern = "**/*.py" if recursive else "*.py"
        python_files = list(directory.glob(pattern))
        
        if not python_files:
            print(f"在 {directory} 中未找到Python文件")
            return
        
        print(f"找到 {len(python_files)} 个Python文件")
        print("=" * 60)
        
        for file_path in python_files:
            self.clean_file(file_path)
    
    def clean_quality_control_files(self) -> None:
        """清理质控系统生成的所有文件"""
        print("开始清理质控系统生成的Python文件...")
        print("=" * 60)
        
        # 清理规则质控文件
        regulatory_dir = self.base_dir / "Regulatory_Quality_Control"
        if regulatory_dir.exists():
            print("清理规则质控文件:")
            self.clean_directory(regulatory_dir)
        
        # 清理内涵质控文件
        connotation_dir = self.base_dir / "Connotation_Quality_Control"
        if connotation_dir.exists():
            print("\n清理内涵质控文件:")
            self.clean_directory(connotation_dir)
    
    def print_summary(self) -> None:
        """打印清理摘要"""
        print("\n" + "=" * 60)
        print("清理摘要:")
        print(f"成功清理的文件数: {len(self.cleaned_files)}")
        print(f"清理失败的文件数: {len(self.error_files)}")
        
        if self.cleaned_files:
            print("\n已清理的文件:")
            for file_path in self.cleaned_files:
                print(f"  - {file_path}")
        
        if self.error_files:
            print("\n清理失败的文件:")
            for file_path, error in self.error_files:
                print(f"  - {file_path}: {error}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='质控代码清理工具')
    parser.add_argument(
        '--file', '-f',
        type=str,
        help='清理指定的Python文件'
    )
    parser.add_argument(
        '--dir', '-d',
        type=str,
        help='清理指定目录中的所有Python文件'
    )
    parser.add_argument(
        '--all', '-a',
        action='store_true',
        help='清理质控系统中的所有生成文件'
    )
    
    args = parser.parse_args()
    
    cleaner = CodeCleaner()
    
    if args.file:
        file_path = Path(args.file)
        if file_path.exists():
            cleaner.clean_file(file_path)
        else:
            print(f"文件不存在: {file_path}")
    elif args.dir:
        dir_path = Path(args.dir)
        cleaner.clean_directory(dir_path)
    elif args.all:
        cleaner.clean_quality_control_files()
    else:
        # 默认清理质控系统文件
        cleaner.clean_quality_control_files()
    
    cleaner.print_summary()

if __name__ == "__main__":
    main()
