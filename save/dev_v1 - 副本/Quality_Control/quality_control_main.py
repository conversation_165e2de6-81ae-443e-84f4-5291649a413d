# -*- coding: utf-8 -*-
"""
质控系统总控制文件
根据文档类型调用相应的质控控制器

支持功能：
1. 内涵质控（Content Quality Control）：基于LLM的医疗文档内容质量评估
2. 规则质控（Rule-based Quality Control）：基于预定义规则的合规性检查
3. 综合质控：整合内涵质控和规则质控结果

支持文档类型：
- 出院小结（Discharge Summary）
- 初次病程记录（Initial Progress Note）
"""
import json
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Union

# 添加父目录到路径
sys.path.append(str(Path(__file__).parent.parent))

class QualityControlMain:
    """
    质控系统主控制器

    负责协调和管理不同类型文档的质控检查，包括：
    - 内涵质控：基于AI模型的内容质量评估
    - 规则质控：基于预定义规则的合规性检查
    - 综合质控：整合两种质控结果
    """

    def __init__(self):
        """初始化质控主控制器"""
        self.base_dir = Path(__file__).parent
        self.json_dir = self.base_dir.parent / "rule_type" / "rule_type_json"

        # 文档类型映射表
        self.document_type_mapping = {}
        self._load_document_mappings()

        # 控制器映射
        self.controllers = {}
        self._load_controllers()

        # 支持的质控类型
        self.quality_control_types = {
            "content": "内涵质控",
            "rule": "规则质控",
            "integrated": "综合质控"
        }
    
    def _load_document_mappings(self):
        """
        加载文档类型映射

        从JSON配置文件中加载支持的文档类型映射关系
        """
        try:
            for json_file in self.json_dir.glob("*.json"):
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    chinese_name = data['metadata']['document_type_chinese']
                    english_name = data['metadata']['document_type_english']
                    self.document_type_mapping[chinese_name] = english_name
                    print(f"✓ 加载文档类型映射: {chinese_name} -> {english_name}")
        except Exception as e:
            print(f"✗ 加载文档类型映射失败: {e}")

    def _load_controllers(self):
        """
        动态加载控制器

        根据文档类型映射动态加载对应的质控控制器
        """
        for chinese_name, english_name in self.document_type_mapping.items():
            try:
                controller_name = f"{english_name.lower().replace(' ', '_')}_controller"
                controller_module = __import__(controller_name, fromlist=[controller_name])

                # 实例化控制器类
                if english_name == "Discharge Summary":
                    controller_class = getattr(controller_module, 'DischargeSummaryController')
                    self.controllers[chinese_name] = controller_class()
                elif english_name == "Initial Progress Note":
                    controller_class = getattr(controller_module, 'InitialProgressNoteController')
                    self.controllers[chinese_name] = controller_class()
                else:
                    # 通用控制器实例化逻辑
                    class_name = ''.join(word.capitalize() for word in english_name.split()) + 'Controller'
                    controller_class = getattr(controller_module, class_name)
                    self.controllers[chinese_name] = controller_class()

                print(f"✓ 加载控制器: {chinese_name} -> {controller_name}")
            except (ImportError, AttributeError) as e:
                print(f"✗ 无法加载控制器 {controller_name}: {e}")
    
    def run_content_quality_control(self, document_type_chinese: str, medical_record: dict) -> dict:
        """
        运行内涵质控检查

        Args:
            document_type_chinese (str): 中文文档类型，如"出院记录"、"首次病程记录"
            medical_record (dict): 病历数据

        Returns:
            dict: 内涵质控结果
        """
        if document_type_chinese not in self.document_type_mapping:
            return {
                "error": f"不支持的文档类型: {document_type_chinese}",
                "supported_types": list(self.document_type_mapping.keys())
            }

        english_name = self.document_type_mapping[document_type_chinese]

        if document_type_chinese not in self.controllers:
            return {
                "error": f"控制器未加载: {document_type_chinese}",
                "document_type_english": english_name
            }

        try:
            controller = self.controllers[document_type_chinese]
            result = controller.run_connotation_quality_control(medical_record)

            # 添加元数据
            metadata = {
                "document_type_chinese": document_type_chinese,
                "document_type_english": english_name,
                "quality_control_type": "内涵质控",
                "quality_control_type_english": "Content Quality Control",
                "total_rules": len(result),
                "timestamp": datetime.now().isoformat()
            }

            return {
                "connotation_results": result,
                "metadata": metadata
            }

        except Exception as e:
            return {
                "error": f"内涵质控执行失败: {str(e)}",
                "document_type_chinese": document_type_chinese,
                "document_type_english": english_name
            }

    def run_rule_quality_control(self, document_type_chinese: str, medical_record: dict) -> dict:
        """
        运行规则质控检查

        Args:
            document_type_chinese (str): 中文文档类型，如"出院记录"、"首次病程记录"
            medical_record (dict): 病历数据

        Returns:
            dict: 规则质控结果
        """
        if document_type_chinese not in self.document_type_mapping:
            return {
                "error": f"不支持的文档类型: {document_type_chinese}",
                "supported_types": list(self.document_type_mapping.keys())
            }

        english_name = self.document_type_mapping[document_type_chinese]

        if document_type_chinese not in self.controllers:
            return {
                "error": f"控制器未加载: {document_type_chinese}",
                "document_type_english": english_name
            }

        try:
            controller = self.controllers[document_type_chinese]
            result = controller.run_regulatory_quality_control(medical_record)

            # 添加元数据
            metadata = {
                "document_type_chinese": document_type_chinese,
                "document_type_english": english_name,
                "quality_control_type": "规则质控",
                "quality_control_type_english": "Rule-based Quality Control",
                "total_rules": len(result),
                "timestamp": datetime.now().isoformat()
            }

            return {
                "regulatory_results": result,
                "metadata": metadata
            }

        except Exception as e:
            return {
                "error": f"规则质控执行失败: {str(e)}",
                "document_type_chinese": document_type_chinese,
                "document_type_english": english_name
            }
    
    def run_integrated_quality_control(self, document_type_chinese: str, medical_record: dict) -> dict:
        """
        运行综合质控检查（内涵质控 + 规则质控）

        Args:
            document_type_chinese (str): 中文文档类型，如"出院记录"、"首次病程记录"
            medical_record (dict): 病历数据

        Returns:
            dict: 综合质控结果
        """
        if document_type_chinese not in self.document_type_mapping:
            return {
                "error": f"不支持的文档类型: {document_type_chinese}",
                "supported_types": list(self.document_type_mapping.keys())
            }

        english_name = self.document_type_mapping[document_type_chinese]

        if document_type_chinese not in self.controllers:
            return {
                "error": f"控制器未加载: {document_type_chinese}",
                "document_type_english": english_name
            }

        try:
            controller = self.controllers[document_type_chinese]
            result = controller.run_integrated_quality_control(medical_record)

            # 添加元数据
            result["metadata"] = {
                "document_type_chinese": document_type_chinese,
                "document_type_english": english_name,
                "quality_control_type": "综合质控",
                "quality_control_type_english": "Integrated Quality Control",
                "total_rules": len(result.get("regulatory_results", {})) + len(result.get("connotation_results", {})),
                "regulatory_count": len(result.get("regulatory_results", {})),
                "connotation_count": len(result.get("connotation_results", {})),
                "timestamp": datetime.now().isoformat()
            }

            return result

        except Exception as e:
            return {
                "error": f"综合质控执行失败: {str(e)}",
                "document_type_chinese": document_type_chinese,
                "document_type_english": english_name
            }

    # 保持向后兼容性
    def run_quality_control(self, document_type_chinese: str, medical_record: dict) -> dict:
        """
        运行质控检查（默认为综合质控，保持向后兼容性）

        Args:
            document_type_chinese (str): 中文文档类型
            medical_record (dict): 病历数据

        Returns:
            dict: 综合质控结果
        """
        return self.run_integrated_quality_control(document_type_chinese, medical_record)

    def get_supported_document_types(self) -> dict:
        """
        获取支持的文档类型列表

        Returns:
            dict: 包含支持的文档类型信息
        """
        return {
            "chinese_types": list(self.document_type_mapping.keys()),
            "english_types": list(self.document_type_mapping.values()),
            "mappings": self.document_type_mapping,
            "total_count": len(self.document_type_mapping)
        }

    def get_supported_quality_control_types(self) -> dict:
        """
        获取支持的质控类型列表

        Returns:
            dict: 包含支持的质控类型信息
        """
        return {
            "types": self.quality_control_types,
            "descriptions": {
                "content": "基于AI模型的医疗文档内容质量评估，关注医疗记录的专业性、完整性和准确性",
                "rule": "基于预定义规则的合规性检查，确保文档符合医疗规范和标准",
                "integrated": "综合内涵质控和规则质控的全面质量评估"
            }
        }
    
    def run_standardized_quality_control(self, type_param: str, data: dict) -> dict:
        """
        运行标准化质控检查，处理新的测试数据集格式

        Args:
            type_param (str): 文档类型参数
                - "Discharge Summary" - 处理出院小结数据
                - "Initial Progress Note" - 处理首次病程记录数据
            data (dict): JSON对象，包含Patient、VisitInfo、RepSummaryInfo/CaseHistoryInfo结构

        Returns:
            dict: 标准化质控报告，包含quality_score、quality_status、quality_issues、quality_suggestions
        """
        try:
            # 映射文档类型
            type_mapping = {
                "Discharge Summary": "出院记录",
                "Initial Progress Note": "首次病程记录"
            }

            if type_param not in type_mapping:
                return {
                    "error": f"不支持的文档类型: {type_param}",
                    "supported_types": list(type_mapping.keys())
                }

            document_type_chinese = type_mapping[type_param]

            # 验证数据结构
            if not self._validate_data_structure(type_param, data):
                return {
                    "error": "数据结构验证失败",
                    "required_structure": self._get_required_structure(type_param)
                }

            # 转换数据格式为控制器可处理的格式
            medical_record = self._convert_to_medical_record(type_param, data)

            # 运行综合质控
            raw_results = self.run_integrated_quality_control(document_type_chinese, medical_record)

            if "error" in raw_results:
                return raw_results

            # 转换为标准化报告格式
            standardized_report = self._convert_to_standardized_report(raw_results)

            return standardized_report

        except Exception as e:
            return {
                "error": f"标准化质控执行失败: {str(e)}",
                "type_param": type_param
            }

    def _validate_data_structure(self, type_param: str, data: dict) -> bool:
        """
        验证数据结构是否符合要求

        Args:
            type_param (str): 文档类型参数
            data (dict): 输入数据

        Returns:
            bool: 验证结果
        """
        try:
            # 检查基本结构
            if not isinstance(data, dict):
                return False

            # 检查必需的顶层字段
            required_fields = ["Patient", "VisitInfo"]
            for field in required_fields:
                if field not in data:
                    return False

            # 检查Patient字段
            patient = data.get("Patient", {})
            patient_required = ["PatientId", "PatientName", "Age", "SexName"]
            for field in patient_required:
                if field not in patient:
                    return False

            # 检查VisitInfo字段
            visit_info = data.get("VisitInfo", {})
            visit_required = ["AdmissionNumber", "DeptName"]
            for field in visit_required:
                if field not in visit_info:
                    return False

            # 根据文档类型检查特定字段
            if type_param == "Discharge Summary":
                if "RepSummaryInfo" not in data:
                    return False
                rep_info = data.get("RepSummaryInfo", {})
                rep_required = ["AssayResult", "IsTumor", "TreatmentOutcomes"]
                for field in rep_required:
                    if field not in rep_info:
                        return False

            elif type_param == "Initial Progress Note":
                if "CaseHistoryInfo" not in data:
                    return False
                case_info = data.get("CaseHistoryInfo", {})
                case_required = ["ChiefComplaint", "Diagnosis", "TreatmentPlan"]
                for field in case_required:
                    if field not in case_info:
                        return False

            return True

        except Exception:
            return False

    def _get_required_structure(self, type_param: str) -> dict:
        """
        获取要求的数据结构说明

        Args:
            type_param (str): 文档类型参数

        Returns:
            dict: 结构说明
        """
        base_structure = {
            "Patient": {
                "PatientId": "string",
                "PatientName": "string",
                "Age": "int",
                "SexName": "string"
            },
            "VisitInfo": {
                "AdmissionNumber": "string",
                "DeptName": "string",
                "其他字段": "可选"
            }
        }

        if type_param == "Discharge Summary":
            base_structure["RepSummaryInfo"] = {
                "AssayResult": "string",
                "IsTumor": "string",
                "TreatmentOutcomes": "string",
                "其他字段": "可选"
            }
        elif type_param == "Initial Progress Note":
            base_structure["CaseHistoryInfo"] = {
                "ChiefComplaint": "string",
                "Diagnosis": "string",
                "TreatmentPlan": "string",
                "其他字段": "可选"
            }

        return base_structure

    def _convert_to_medical_record(self, type_param: str, data: dict) -> dict:
        """
        将新格式数据转换为控制器可处理的医疗记录格式

        Args:
            type_param (str): 文档类型参数
            data (dict): 输入数据

        Returns:
            dict: 转换后的医疗记录格式
        """
        patient = data.get("Patient", {})
        visit_info = data.get("VisitInfo", {})

        # 基础医疗记录结构
        medical_record = {
            "patient_id": patient.get("PatientId", ""),
            "patient_name": patient.get("PatientName", ""),
            "age": patient.get("Age", 0),
            "sex": patient.get("SexName", ""),
            "admission_number": visit_info.get("AdmissionNumber", ""),
            "department": visit_info.get("DeptName", ""),
            "ward": visit_info.get("WardName", ""),
            "bed_no": visit_info.get("BedNo", ""),
            "admission_date": visit_info.get("InHospitalDateTime", ""),
            "attending_doctor": visit_info.get("AttendingDocName", ""),
            "chief_doctor": visit_info.get("ChiefDocName", ""),
            "bed_nurse": visit_info.get("BedNurName", "")
        }

        # 根据文档类型添加特定内容
        if type_param == "Discharge Summary":
            rep_info = data.get("RepSummaryInfo", {})
            medical_record.update({
                "assay_result": rep_info.get("AssayResult", ""),
                "examinations_consultation": rep_info.get("ExaminationsAndConsultation", ""),
                "is_tumor": rep_info.get("IsTumor", ""),
                "course_treatment_outcomes": rep_info.get("CourseTreatmentOutcomes", ""),
                "complication": rep_info.get("Complication", ""),
                "discharge_disease": rep_info.get("DisHospitalDisease", ""),
                "discharge_drugs": rep_info.get("DisHospitalDrugs", ""),
                "treatment_outcomes": rep_info.get("TreatmentOutcomes", ""),
                "content": f"患者{patient.get('PatientName', '')}，{patient.get('SexName', '')}，{patient.get('Age', '')}岁。{rep_info.get('CourseTreatmentOutcomes', '')}"
            })

        elif type_param == "Initial Progress Note":
            case_info = data.get("CaseHistoryInfo", {})
            medical_record.update({
                "chief_complaint": case_info.get("ChiefComplaint", ""),
                "physical_exam": case_info.get("PhysicalExam", ""),
                "auxiliary_exam": case_info.get("AuxiliaryExam", ""),
                "diagnosis": case_info.get("Diagnosis", ""),
                "assessment": case_info.get("Assessment", ""),
                "differential_diagnosis": case_info.get("DifferentialDiagnosis", ""),
                "treatment_plan": case_info.get("TreatmentPlan", ""),
                "content": f"患者{patient.get('PatientName', '')}，{patient.get('SexName', '')}，{patient.get('Age', '')}岁。主诉：{case_info.get('ChiefComplaint', '')}。诊断：{case_info.get('Diagnosis', '')}。"
            })

        return medical_record

    def _convert_to_standardized_report(self, raw_results: dict) -> dict:
        """
        将原始质控结果转换为标准化报告格式

        Args:
            raw_results (dict): 原始质控结果

        Returns:
            dict: 标准化质控报告
        """
        try:
            # 初始化标准化报告
            standardized_report = {
                "quality_score": {},
                "quality_status": "通过",
                "quality_issues": {},
                "quality_suggestions": {},
                "metadata": raw_results.get("metadata", {})
            }

            total_score = 100  # 基础分100分
            total_deductions = 0
            all_issues = {}
            all_suggestions = {}
            score_details = {}

            # 处理规则质控结果
            regulatory_results = raw_results.get("regulatory_results", {})
            for rule_name, result in regulatory_results.items():
                deduction = result.get("deduction_points", 0)
                has_problem = result.get("has_problem", False)

                if has_problem:
                    total_deductions += deduction
                    all_issues[rule_name] = f"缺失{rule_name}"
                    all_suggestions[rule_name] = f"请补充{rule_name}"
                    score_details[rule_name] = f"扣分: {deduction}分"
                else:
                    all_issues[rule_name] = "无"
                    all_suggestions[rule_name] = "无"
                    score_details[rule_name] = "扣分: 0分"

            # 处理内涵质控结果
            connotation_results = raw_results.get("connotation_results", {})
            for rule_name, result in connotation_results.items():
                score = result.get("score", 0)
                max_score = result.get("deduction_points", result.get("max_points", 0))
                problems = result.get("problems", "")
                suggestions = result.get("suggestions", "")

                # 计算扣分（如果有最大分数）
                if max_score > 0:
                    deduction = max(0, max_score - score)
                    total_deductions += deduction
                    score_details[rule_name] = f"扣分: {deduction}分"
                else:
                    score_details[rule_name] = f"得分: {score}分"

                # 处理问题和建议
                if problems and problems.strip() and problems.lower() not in ["无", "无问题", "none", ""]:
                    all_issues[rule_name] = problems
                else:
                    all_issues[rule_name] = "无"

                if suggestions and suggestions.strip() and suggestions.lower() not in ["无", "无建议", "none", ""]:
                    all_suggestions[rule_name] = suggestions
                else:
                    all_suggestions[rule_name] = "无"

            # 计算最终得分
            final_score = max(0, total_score - total_deductions)

            # 设置质控分数
            standardized_report["quality_score"] = {
                "total_score": final_score,
                "base_score": total_score,
                "total_deductions": total_deductions,
                "score_details": score_details
            }

            # 设置质控状态
            standardized_report["quality_status"] = "通过" if final_score >= 60 else "不通过"

            # 设置质控问题和建议
            standardized_report["quality_issues"] = all_issues
            standardized_report["quality_suggestions"] = all_suggestions

            # 添加统计信息
            standardized_report["statistics"] = {
                "total_rules": len(regulatory_results) + len(connotation_results),
                "regulatory_rules": len(regulatory_results),
                "connotation_rules": len(connotation_results),
                "total_problems": len([v for v in all_issues.values() if v != "无"]),
                "pass_threshold": 60,
                "quality_grade": self._get_quality_grade(final_score)
            }

            return standardized_report

        except Exception as e:
            return {
                "error": f"转换标准化报告失败: {str(e)}",
                "raw_results": raw_results
            }

    def _get_quality_grade(self, score: float) -> str:
        """
        根据分数获取质量等级

        Args:
            score (float): 质控分数

        Returns:
            str: 质量等级
        """
        if score >= 95:
            return "优秀"
        elif score >= 85:
            return "良好"
        elif score >= 75:
            return "合格"
        elif score >= 60:
            return "需改进"
        else:
            return "不合格"

    def get_quality_control_summary(self, results: dict) -> dict:
        """
        生成质控结果摘要

        Args:
            results (dict): 质控结果

        Returns:
            dict: 质控摘要
        """
        if "error" in results:
            return {"error": results["error"]}

        summary = {
            "document_info": results.get("metadata", {}),
            "overall_score": 0,
            "total_deductions": 0,
            "problem_count": 0,
            "regulatory_summary": {},
            "connotation_summary": {},
            "recommendations": [],
            "quality_grade": "未评估"
        }

        # 统计规则质控结果
        regulatory_results = results.get("regulatory_results", {})
        regulatory_problems = 0
        regulatory_deductions = 0

        for rule, result in regulatory_results.items():
            if result.get("has_problem", False):
                regulatory_problems += 1
                regulatory_deductions += result.get("deduction_points", 0)

        summary["regulatory_summary"] = {
            "total_rules": len(regulatory_results),
            "problem_count": regulatory_problems,
            "total_deductions": regulatory_deductions,
            "compliance_rate": (len(regulatory_results) - regulatory_problems) / len(regulatory_results) * 100 if regulatory_results else 100
        }

        # 统计内涵质控结果
        connotation_results = results.get("connotation_results", {})
        connotation_total_score = 0
        connotation_max_score = 0
        connotation_problems = 0

        for rule, result in connotation_results.items():
            connotation_total_score += result.get("score", 0)
            connotation_max_score += result.get("deduction_points", result.get("max_points", 0))
            if result.get("problems"):
                connotation_problems += 1
                summary["recommendations"].append(result.get("suggestions", ""))

        summary["connotation_summary"] = {
            "total_rules": len(connotation_results),
            "total_score": connotation_total_score,
            "max_score": connotation_max_score,
            "score_rate": connotation_total_score / connotation_max_score * 100 if connotation_max_score > 0 else 100,
            "problem_count": connotation_problems
        }

        # 计算总体评分和等级
        summary["total_deductions"] = regulatory_deductions
        summary["problem_count"] = regulatory_problems + connotation_problems

        # 计算综合得分（基础分100分）
        base_score = 100
        final_score = max(0, base_score - regulatory_deductions + (connotation_total_score - connotation_max_score))
        summary["overall_score"] = final_score

        # 确定质量等级
        if final_score >= 95:
            summary["quality_grade"] = "优秀"
        elif final_score >= 85:
            summary["quality_grade"] = "良好"
        elif final_score >= 75:
            summary["quality_grade"] = "合格"
        elif final_score >= 60:
            summary["quality_grade"] = "需改进"
        else:
            summary["quality_grade"] = "不合格"

        return summary

def main():
    """
    主函数示例

    演示质控系统的各种功能，包括：
    1. 内涵质控
    2. 规则质控
    3. 综合质控
    4. 标准化质控（新增）
    """
    print("=" * 60)
    print("医疗文档质控系统演示")
    print("=" * 60)

    qc_main = QualityControlMain()

    # 显示支持的文档类型
    supported_types = qc_main.get_supported_document_types()
    print(f"\n📋 支持的文档类型 (共{supported_types['total_count']}种):")
    for chinese, english in supported_types["mappings"].items():
        print(f"  • {chinese} ({english})")

    # 显示支持的质控类型
    qc_types = qc_main.get_supported_quality_control_types()
    print(f"\n🔍 支持的质控类型:")
    for type_key, type_name in qc_types["types"].items():
        description = qc_types["descriptions"][type_key]
        print(f"  • {type_name}: {description}")

    # 测试新的标准化质控功能
    print(f"\n" + "=" * 60)
    print("🆕 测试标准化质控功能（处理新测试数据集）")
    print("=" * 60)

    # 加载测试数据
    test_data_path = Path(__file__).parent.parent / "data"

    # 测试出院小结数据
    try:
        discharge_file = test_data_path / "test_discharge_summary.json"
        if discharge_file.exists():
            with open(discharge_file, 'r', encoding='utf-8') as f:
                discharge_data = json.load(f)

            print(f"\n📄 测试出院小结数据 (共{len(discharge_data['Data'])}条记录)")

            # 测试第一条记录
            first_record = discharge_data['Data'][0]
            print(f"患者: {first_record['Patient']['PatientName']} ({first_record['Patient']['Age']}岁, {first_record['Patient']['SexName']})")
            print(f"科室: {first_record['VisitInfo']['DeptName']}")

            # 运行标准化质控
            result = qc_main.run_standardized_quality_control("Discharge Summary", first_record)

            if "error" in result:
                print(f"❌ 错误: {result['error']}")
            else:
                print("✅ 标准化质控完成")
                print(f"🏆 质控分数: {result['quality_score']['total_score']}/100")
                print(f"📊 质控状态: {result['quality_status']}")
                print(f"📈 质量等级: {result['statistics']['quality_grade']}")
                print(f"⚠️  问题总数: {result['statistics']['total_problems']}")

                # 显示部分问题和建议
                issues_count = 0
                for rule, issue in result['quality_issues'].items():
                    if issue != "无" and issues_count < 3:
                        print(f"  问题: {rule} - {issue}")
                        print(f"  建议: {result['quality_suggestions'][rule]}")
                        issues_count += 1
        else:
            print("❌ 未找到出院小结测试数据文件")

    except Exception as e:
        print(f"❌ 出院小结测试失败: {str(e)}")

    # 测试首次病程记录数据
    try:
        course_file = test_data_path / "test_first_course_record.json"
        if course_file.exists():
            with open(course_file, 'r', encoding='utf-8') as f:
                course_data = json.load(f)

            print(f"\n📄 测试首次病程记录数据 (共{len(course_data['Data'])}条记录)")

            # 测试第一条记录
            first_record = course_data['Data'][0]
            print(f"患者: {first_record['Patient']['PatientName']} ({first_record['Patient']['Age']}岁, {first_record['Patient']['SexName']})")
            print(f"科室: {first_record['VisitInfo']['DeptName']}")
            print(f"主诉: {first_record['CaseHistoryInfo']['ChiefComplaint']}")

            # 运行标准化质控
            result = qc_main.run_standardized_quality_control("Initial Progress Note", first_record)

            if "error" in result:
                print(f"❌ 错误: {result['error']}")
            else:
                print("✅ 标准化质控完成")
                print(f"🏆 质控分数: {result['quality_score']['total_score']}/100")
                print(f"📊 质控状态: {result['quality_status']}")
                print(f"📈 质量等级: {result['statistics']['quality_grade']}")
                print(f"⚠️  问题总数: {result['statistics']['total_problems']}")
        else:
            print("❌ 未找到首次病程记录测试数据文件")

    except Exception as e:
        print(f"❌ 首次病程记录测试失败: {str(e)}")

    # 传统质控测试（保持向后兼容性）
    print(f"\n" + "=" * 60)
    print("🔄 传统质控功能测试（向后兼容性）")
    print("=" * 60)

    # 测试示例数据
    test_record = {
        "patient_id": "P20240805001",
        "patient_name": "张三",
        "content": """
        患者张三，男，45岁，因"胸痛3天"入院。
        入院诊断：冠心病，心绞痛
        诊疗经过：给予抗血小板聚集、调脂稳斑等治疗
        出院诊断：冠心病，心绞痛（稳定型）
        出院医嘱：阿司匹林100mg qd，阿托伐他汀20mg qn
        出院情况：症状缓解，病情稳定
        """,
        "admission_date": "2024-08-01",
        "discharge_date": "2024-08-05",
        "diagnosis": {
            "admission": "冠心病，心绞痛",
            "discharge": "冠心病，心绞痛（稳定型）"
        },
        "treatment": {
            "medications": ["阿司匹林", "阿托伐他汀"],
            "procedures": []
        }
    }

    # 测试不同类型的质控
    test_cases = [
        ("出院记录", "综合质控"),
        ("出院记录", "内涵质控"),
        ("出院记录", "规则质控")
    ]

    for doc_type, qc_type in test_cases:
        print(f"\n🏥 测试 {doc_type} - {qc_type}")

        try:
            if qc_type == "综合质控":
                results = qc_main.run_integrated_quality_control(doc_type, test_record)
            elif qc_type == "内涵质控":
                results = qc_main.run_content_quality_control(doc_type, test_record)
            elif qc_type == "规则质控":
                results = qc_main.run_rule_quality_control(doc_type, test_record)

            if "error" in results:
                print(f"❌ 错误: {results['error']}")
            else:
                # 显示基本信息
                metadata = results.get("metadata", {})
                print(f"📄 文档类型: {metadata.get('document_type_chinese')} ({metadata.get('document_type_english')})")
                print(f"🔍 质控类型: {metadata.get('quality_control_type')} ({metadata.get('quality_control_type_english')})")
                print(f"📊 规则总数: {metadata.get('total_rules', 0)}")

                # 生成摘要（仅对综合质控）
                if qc_type == "综合质控":
                    summary = qc_main.get_quality_control_summary(results)
                    print(f"🏆 综合得分: {summary['overall_score']:.1f}/100")
                    print(f"📈 质量等级: {summary['quality_grade']}")
                    print(f"⚠️  问题总数: {summary['problem_count']}")

                print("✅ 质控完成")

        except Exception as e:
            print(f"❌ 执行失败: {str(e)}")

if __name__ == "__main__":
    main()
