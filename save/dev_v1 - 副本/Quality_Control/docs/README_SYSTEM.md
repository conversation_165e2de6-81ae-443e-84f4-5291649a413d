# 医疗质控系统

## 系统概述

本系统是一个完整的医疗质控解决方案，支持规则质控和内涵质控两种质控方式，能够对不同类型的医疗文档进行自动化质量控制检查。

## 系统架构

### 控制层架构

```
Quality_Control/
├── quality_control_main.py                    # 总控制文件
├── discharge_summary_controller.py            # 出院记录控制器
├── initial_progress_note_controller.py        # 首次病程记录控制器
├── Regulatory_Quality_Control/                # 规则质控目录
│   ├── Discharge_Summary/
│   │   └── discharge_summary.py              # 出院记录规则质控
│   └── Initial_Progress_Note/
│       └── initial_progress_note.py          # 首次病程记录规则质控
└── Connotation_Quality_Control/               # 内涵质控目录
    ├── Discharge_Summary/
    │   ├── discharge_summary.py              # 出院记录内涵质控
    │   └── discharge_summary_prompt.json     # 出院记录内涵质控提示
    └── Initial_Progress_Note/
        ├── initial_progress_note.py          # 首次病程记录内涵质控
        └── initial_progress_note_prompt.json # 首次病程记录内涵质控提示
```

### 核心组件

1. **总控制文件 (quality_control_main.py)**
   - 接收中文文档类型作为入参（如"出院记录"、"首次病程记录"）
   - 自动映射到对应的英文文档类型
   - 调用相应的文档类型控制器
   - 提供统一的质控接口

2. **文档类型控制器**
   - `discharge_summary_controller.py`: 出院记录质控控制器
   - `initial_progress_note_controller.py`: 首次病程记录质控控制器
   - 负责整合规则质控和内涵质控结果
   - 生成详细的质控报告和评分

3. **规则质控模块**
   - 实现二元判断逻辑（有问题/无问题）
   - 检查时效性、段落完整性、签名合理性等
   - 使用 qwen_32B_config 模型配置

4. **内涵质控模块**
   - 使用大模型进行深度内容分析
   - 输出质控分数、质控问题、质控建议
   - 使用 qwen_32B_config 进行运行时调用
   - 使用 glm_code_config 进行代码生成

## 支持的文档类型

### 1. 出院记录 (Discharge Summary)

**规则质控项目：**
- 时效性检查：出院记录是否在出院后24小时内完成
- 段落完整性检查：诊疗经过、入院诊断、入院情况、出院诊断、出院情况、出院日期、入院日期
- 签名合理性检查：住院医师、主治医师签名

**内涵质控项目：**
- 出院医嘱完整性：药名、剂量、用法、带药总量及随访要求等

### 2. 首次病程记录 (Initial Progress Note)

**规则质控项目：**
- 时效性检查：首次病程是否在患者入院后8小时内完成
- 段落完整性检查：病例特点、初步诊断、诊断依据、鉴别诊断、诊疗计划

**内涵质控项目：**
- 雷同率检查：首次病程记录与主任医师首次查房记录的雷同情况

## 使用方法

### 1. 基本使用

```python
from quality_control_main import QualityControlMain

# 创建主控制器
qc_main = QualityControlMain()

# 准备病历数据
medical_record = {
    "patient_id": "P001",
    "patient_name": "张三",
    "content": "病历内容...",
    "discharge_time": "2024-01-10 10:00:00",
    "record_completion_time": "2024-01-10 15:00:00"
}

# 运行质控
results = qc_main.run_quality_control("出院记录", medical_record)

# 获取质控摘要
summary = qc_main.get_quality_control_summary(results)
print(summary)
```

### 2. 使用特定控制器

```python
from discharge_summary_controller import DischargeSummaryController

# 创建出院记录控制器
controller = DischargeSummaryController()

# 生成详细报告
report = controller.get_quality_control_report(medical_record)
print(f"合规评分: {report['compliance_score']}")
```

### 3. 运行测试

```bash
python test_quality_control_system.py
```

## 质控结果格式

### 规则质控结果
```json
{
  "rule_content": {
    "rule_type": "时效性",
    "classification": "手术科室", 
    "deduction_points": 60.0,
    "has_problem": false,
    "type": "regulatory"
  }
}
```

### 内涵质控结果
```json
{
  "rule_content": {
    "rule_type": "段落完整性",
    "classification": "手术科室",
    "max_points": 30.0,
    "score": 25,
    "problems": "需要检查出院医嘱的完整性",
    "suggestions": "建议完善出院医嘱，包括药名、剂量、用法等信息",
    "type": "connotation"
  }
}
```

### 综合质控报告
```json
{
  "patient_info": {
    "patient_id": "P001",
    "patient_name": "张三",
    "document_type": "出院记录"
  },
  "compliance_score": {
    "final_score": 85.5,
    "base_score": 100,
    "regulatory_deduction": 15.0,
    "connotation_bonus": 0.5,
    "grade": "良好"
  },
  "summary": {
    "total_rules_checked": 10,
    "regulatory_issues": 2,
    "connotation_issues": 1,
    "overall_status": "轻微问题"
  }
}
```

## 评分系统

### 合规评分计算
- 基础分：100分
- 规则质控扣分：根据问题严重程度扣分
- 内涵质控加分：得分率超过80%时给予加分
- 最终评分 = 基础分 - 规则质控扣分 + 内涵质控加分

### 等级评定
- 优秀：95分及以上
- 良好：85-94分
- 合格：75-84分
- 基本合格：60-74分
- 不合格：60分以下

## 模型配置

### 使用的模型配置
- **规则质控代码生成**: `qwen_32B_config`
- **内涵质控代码生成**: `glm_code_config`
- **内涵质控运行时**: `qwen_32B_config`

### 配置文件位置
- 模型配置：`../config.py`
- 模型调用：`../model_use.py`

## 扩展指南

### 添加新的文档类型

1. 在 `rule_type/rule_type_json/` 目录下添加新的JSON配置文件
2. 创建对应的规则质控模块
3. 创建对应的内涵质控模块
4. 创建文档类型控制器
5. 更新主控制器的映射关系

### 添加新的质控规则

1. 在对应的规则质控模块中添加检查函数
2. 在 `run_regulatory_quality_control` 函数中注册新规则
3. 更新测试用例

### 添加新的内涵质控项目

1. 创建新的prompt文件
2. 在内涵质控模块中添加处理函数
3. 在 `run_connotation_quality_control` 函数中注册新项目

## 测试结果

系统测试显示：
- ✅ 规则质控功能正常
- ✅ 内涵质控功能正常  
- ✅ 控制层架构工作正常
- ✅ 结果整合功能正常
- ✅ 评分系统功能正常

平均合规评分：79.33分

## 注意事项

1. 确保模型配置正确，API密钥有效
2. 病历数据格式需要符合系统要求
3. 内涵质控需要网络连接调用大模型
4. 建议定期更新质控规则和标准

## 技术支持

如有问题，请检查：
1. 模型配置是否正确
2. 网络连接是否正常
3. 病历数据格式是否符合要求
4. 查看系统日志获取详细错误信息
