# -*- coding: utf-8 -*-
"""
标准化质控API使用示例
演示如何使用新的标准化质控功能处理测试数据集
"""
import json
import sys
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent))

from quality_control_main import QualityControlMain

def example_single_record_quality_control():
    """示例1: 单条记录质控"""
    print("=" * 60)
    print("示例1: 单条记录质控")
    print("=" * 60)
    
    # 初始化质控系统
    qc_main = QualityControlMain()
    
    # 加载出院小结测试数据
    data_path = Path(__file__).parent.parent / "data" / "test_discharge_summary.json"
    
    try:
        with open(data_path, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        
        # 获取第一条记录
        record = test_data['Data'][0]
        
        print(f"患者信息:")
        print(f"  姓名: {record['Patient']['PatientName']}")
        print(f"  年龄: {record['Patient']['Age']}岁")
        print(f"  性别: {record['Patient']['SexName']}")
        print(f"  科室: {record['VisitInfo']['DeptName']}")
        print(f"  住院号: {record['VisitInfo']['AdmissionNumber']}")
        
        # 运行标准化质控
        print(f"\n🔍 正在执行质控检查...")
        result = qc_main.run_standardized_quality_control("Discharge Summary", record)
        
        if "error" in result:
            print(f"❌ 质控失败: {result['error']}")
        else:
            print_quality_control_result(result)
            
    except Exception as e:
        print(f"❌ 示例执行失败: {str(e)}")

def example_batch_quality_control():
    """示例2: 批量质控处理"""
    print("\n" + "=" * 60)
    print("示例2: 批量质控处理")
    print("=" * 60)
    
    qc_main = QualityControlMain()
    
    # 处理出院小结数据
    print("\n📄 处理出院小结数据:")
    discharge_results = batch_process_file(
        qc_main, 
        "dev_v1/data/test_discharge_summary.json", 
        "Discharge Summary"
    )
    
    # 处理首次病程记录数据
    print("\n📄 处理首次病程记录数据:")
    course_results = batch_process_file(
        qc_main, 
        "dev_v1/data/test_first_course_record.json", 
        "Initial Progress Note"
    )
    
    # 生成批量处理报告
    print("\n📊 批量处理统计报告:")
    generate_batch_report(discharge_results, course_results)

def batch_process_file(qc_main, file_path, doc_type):
    """批量处理文件中的所有记录"""
    results = []
    
    try:
        full_path = Path(__file__).parent.parent.parent / file_path
        with open(full_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"  文件: {file_path}")
        print(f"  记录数: {len(data['Data'])}")
        
        for i, record in enumerate(data['Data'], 1):
            patient_name = record['Patient']['PatientName']
            print(f"  处理第{i}条记录: {patient_name}")
            
            result = qc_main.run_standardized_quality_control(doc_type, record)
            
            if "error" in result:
                results.append({
                    "patient_name": patient_name,
                    "success": False,
                    "error": result["error"]
                })
            else:
                quality_score = result.get('quality_score', {})
                statistics = result.get('statistics', {})
                
                results.append({
                    "patient_name": patient_name,
                    "success": True,
                    "total_score": quality_score.get('total_score', 0),
                    "quality_status": result.get('quality_status', '未知'),
                    "quality_grade": statistics.get('quality_grade', '未评估'),
                    "total_problems": statistics.get('total_problems', 0),
                    "total_rules": statistics.get('total_rules', 0)
                })
        
        return results
        
    except Exception as e:
        print(f"  ❌ 批量处理失败: {str(e)}")
        return []

def generate_batch_report(discharge_results, course_results):
    """生成批量处理报告"""
    all_results = discharge_results + course_results
    
    if not all_results:
        print("  无有效数据生成报告")
        return
    
    # 统计成功率
    successful = [r for r in all_results if r.get('success', False)]
    success_rate = len(successful) / len(all_results) * 100
    
    print(f"  总处理记录数: {len(all_results)}")
    print(f"  成功处理数: {len(successful)}")
    print(f"  成功率: {success_rate:.1f}%")
    
    if successful:
        # 计算平均分数
        avg_score = sum(r['total_score'] for r in successful) / len(successful)
        print(f"  平均质控分数: {avg_score:.1f}/100")
        
        # 统计质控状态
        passed = len([r for r in successful if r['quality_status'] == '通过'])
        pass_rate = passed / len(successful) * 100
        print(f"  质控通过率: {pass_rate:.1f}%")
        
        # 统计质量等级分布
        grades = {}
        for r in successful:
            grade = r['quality_grade']
            grades[grade] = grades.get(grade, 0) + 1
        
        print(f"  质量等级分布:")
        for grade, count in grades.items():
            print(f"    {grade}: {count}例")

def example_error_handling():
    """示例3: 错误处理"""
    print("\n" + "=" * 60)
    print("示例3: 错误处理演示")
    print("=" * 60)
    
    qc_main = QualityControlMain()
    
    # 测试不支持的文档类型
    print("\n🔍 测试不支持的文档类型:")
    result = qc_main.run_standardized_quality_control("Unsupported Type", {})
    print(f"  结果: {'✅ 正确处理错误' if 'error' in result else '❌ 未正确处理'}")
    if 'error' in result:
        print(f"  错误信息: {result['error']}")
    
    # 测试无效数据结构
    print("\n🔍 测试无效数据结构:")
    invalid_data = {"invalid": "structure"}
    result = qc_main.run_standardized_quality_control("Discharge Summary", invalid_data)
    print(f"  结果: {'✅ 正确处理错误' if 'error' in result else '❌ 未正确处理'}")
    if 'error' in result:
        print(f"  错误信息: {result['error']}")
    
    # 测试缺少必填字段
    print("\n🔍 测试缺少必填字段:")
    incomplete_data = {
        "Patient": {
            "PatientName": "测试患者"
            # 缺少其他必填字段
        }
    }
    result = qc_main.run_standardized_quality_control("Discharge Summary", incomplete_data)
    print(f"  结果: {'✅ 正确处理错误' if 'error' in result else '❌ 未正确处理'}")
    if 'error' in result:
        print(f"  错误信息: {result['error']}")

def print_quality_control_result(result):
    """打印质控结果的详细信息"""
    print("\n📋 质控结果:")
    
    # 基本信息
    quality_score = result.get("quality_score", {})
    statistics = result.get("statistics", {})
    
    print(f"  🏆 质控分数: {quality_score.get('total_score', 0)}/100")
    print(f"  📊 质控状态: {result.get('quality_status', '未知')}")
    print(f"  📈 质量等级: {statistics.get('quality_grade', '未评估')}")
    print(f"  🔧 扣分总计: {quality_score.get('total_deductions', 0)}分")
    
    # 统计信息
    print(f"\n📊 统计信息:")
    print(f"  规则总数: {statistics.get('total_rules', 0)}")
    print(f"  问题总数: {statistics.get('total_problems', 0)}")
    print(f"  规则质控: {statistics.get('regulatory_rules', 0)}条")
    print(f"  内涵质控: {statistics.get('connotation_rules', 0)}条")
    print(f"  及格线: {statistics.get('pass_threshold', 60)}分")
    
    # 详细问题和建议
    issues = result.get("quality_issues", {})
    suggestions = result.get("quality_suggestions", {})
    
    print(f"\n📝 质控问题和建议:")
    problem_count = 0
    for rule, issue in issues.items():
        if issue != "无":
            problem_count += 1
            print(f"  {problem_count}. {rule}:")
            print(f"     问题: {issue}")
            print(f"     建议: {suggestions.get(rule, '无')}")
    
    if problem_count == 0:
        print("  🎉 未发现质控问题，文档质量良好！")
    
    # 分数详情
    score_details = quality_score.get("score_details", {})
    if score_details:
        print(f"\n💯 分数详情:")
        for rule, detail in score_details.items():
            print(f"  {rule}: {detail}")

def main():
    """主函数"""
    print("🏥 标准化质控API使用示例")
    
    # 运行各个示例
    example_single_record_quality_control()
    example_batch_quality_control()
    example_error_handling()
    
    print("\n" + "=" * 60)
    print("✅ 所有示例执行完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
