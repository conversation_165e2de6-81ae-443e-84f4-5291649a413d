# -*- coding: utf-8 -*-
"""
测试重构后的质控代码生成器
"""
import sys
from pathlib import Path

# 添加父目录路径以导入质控模块
sys.path.append(str(Path(__file__).parent.parent))

from quality_control_generator import QualityControlGenerator

def test_file_name_normalization():
    """测试文件名标准化功能"""
    print("=" * 60)
    print("测试文件名标准化功能")
    print("=" * 60)
    
    generator = QualityControlGenerator()
    
    test_cases = [
        "discharge_summary.json",
        "discharge_summary",
        "discharge summary",
        "Discharge Summary",
        "DISCHARGE SUMMARY",
        "discharge_summary_type.json",
        "initial_progress_note.json",
        "initial progress note",
        "Initial Progress Note"
    ]
    
    for test_case in test_cases:
        normalized = generator.normalize_filename(test_case)
        print(f"输入: '{test_case}' -> 标准化: '{normalized}'")

def test_json_file_finding():
    """测试JSON文件查找功能"""
    print("\n" + "=" * 60)
    print("测试JSON文件查找功能")
    print("=" * 60)
    
    generator = QualityControlGenerator()
    
    test_cases = [
        "discharge_summary",
        "discharge summary",
        "Discharge Summary",
        "initial_progress_note",
        "initial progress note",
        "Initial Progress Note"
    ]
    
    for test_case in test_cases:
        file_path = generator.find_json_file(test_case)
        if file_path:
            print(f"输入: '{test_case}' -> 找到文件: {file_path.name}")
        else:
            print(f"输入: '{test_case}' -> 未找到文件")

def test_specific_file_generation():
    """测试特定文件的代码生成"""
    print("\n" + "=" * 60)
    print("测试特定文件的代码生成")
    print("=" * 60)
    
    generator = QualityControlGenerator()
    
    # 测试生成出院记录的质控代码
    try:
        print("尝试生成出院记录质控代码...")
        generator.generate_all("discharge summary")
        print("✓ 出院记录质控代码生成成功")
    except Exception as e:
        print(f"✗ 出院记录质控代码生成失败: {e}")

def test_list_available_files():
    """测试列出可用文件功能"""
    print("\n" + "=" * 60)
    print("可用的JSON文件")
    print("=" * 60)
    
    generator = QualityControlGenerator()
    
    print("JSON文件目录:", generator.json_dir)
    print("可用文件:")
    for json_file in generator.json_dir.glob("*.json"):
        print(f"  - {json_file.name}")

def test_load_specific_json():
    """测试加载特定JSON文件"""
    print("\n" + "=" * 60)
    print("测试加载特定JSON文件")
    print("=" * 60)
    
    generator = QualityControlGenerator()
    
    try:
        # 测试加载出院记录文件
        json_files = generator.load_json_files("discharge summary")
        
        for doc_type, data in json_files.items():
            print(f"文档类型: {doc_type}")
            print(f"总记录数: {data['metadata']['total_records']}")
            
            if 'classification_stats' in data['metadata']:
                stats = data['metadata']['classification_stats']
                print(f"分类统计:")
                print(f"  - 规则: {stats.get('规则', 0)}")
                print(f"  - 内涵: {stats.get('内涵', 0)}")
                print(f"  - 混合: {stats.get('规则和内涵', 0)}")
            
            # 显示前几条记录的rule_id
            print(f"前5条记录的rule_id:")
            for i, record in enumerate(data['records'][:5]):
                print(f"  {i+1}. {record['rule_id']}: {record['rule_content']}")
                
    except Exception as e:
        print(f"加载失败: {e}")

def main():
    """主测试函数"""
    print("重构后的质控代码生成器测试")
    print("=" * 80)
    
    # 运行各项测试
    test_file_name_normalization()
    test_json_file_finding()
    test_list_available_files()
    test_load_specific_json()
    
    # 询问是否进行实际代码生成测试
    print("\n" + "=" * 60)
    user_input = input("是否进行实际代码生成测试？(y/n): ").strip().lower()
    
    if user_input == 'y':
        test_specific_file_generation()
    else:
        print("跳过代码生成测试")
    
    print("\n" + "=" * 80)
    print("测试完成！")

if __name__ == "__main__":
    main()
