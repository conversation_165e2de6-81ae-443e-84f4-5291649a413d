# 医疗质控API使用指南

## 概述

本文档介绍如何使用Flask Web应用提供的医疗质控API服务。该API基于标准化质控功能，提供HTTP接口供外部系统调用。

## 快速开始

服务启动后将在 `http://localhost:9086` 提供API服务。

### 3. 验证服务状态

```bash
curl http://localhost:9086/hospical/health
```

## API接口

### 基础信息

- **基础URL**: `http://localhost:9086`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

### 接口列表

| 接口路径 | 方法 | 描述 |
|---------|------|------|
| `/hospical` | GET | API首页和服务信息 |
| `/hospical/health` | GET | 服务健康检查 |
| `/hospical/docs` | GET | API文档 |
| `/hospical/quality_control` | POST | 医疗文档质控检查 |

## 核心接口详解

### POST /hospical/quality_control

执行医疗文档质控检查的核心接口。

#### 请求格式

```json
{
  "type": "Discharge Summary" | "Initial Progress Note",
  "data": {
    "Patient": {
      "PatientId": "string",
      "PatientName": "string",
      "Age": number,
      "SexName": "string"
    },
    "VisitInfo": {
      "AdmissionNumber": "string",
      "DeptName": "string",
      ...
    },
    "RepSummaryInfo": { ... } | "CaseHistoryInfo": { ... }
  }
}
```

#### 参数说明

**type** (string, 必需)
- `"Discharge Summary"`: 处理出院小结数据
- `"Initial Progress Note"`: 处理首次病程记录数据

**data** (object, 必需)
- 医疗记录数据，必须严格遵循测试数据集的JSON结构
- 出院小结：包含 `Patient`、`VisitInfo`、`RepSummaryInfo`
- 首次病程记录：包含 `Patient`、`VisitInfo`、`CaseHistoryInfo`

#### 响应格式

**成功响应 (200)**:
```json
{
  "success": true,
  "data": {
    "quality_score": {
      "total_score": 85,
      "base_score": 100,
      "total_deductions": 15,
      "score_details": {
        "规则名称1": "扣分: 5分",
        "规则名称2": "扣分: 0分"
      }
    },
    "quality_status": "通过",
    "quality_issues": {
      "规则名称1": "缺失主要化验结果",
      "规则名称2": "无"
    },
    "quality_suggestions": {
      "规则名称1": "请补充主要化验结果",
      "规则名称2": "无"
    },
    "statistics": {
      "total_rules": 10,
      "regulatory_rules": 6,
      "connotation_rules": 4,
      "total_problems": 2,
      "pass_threshold": 60,
      "quality_grade": "良好"
    },
    "metadata": {
      "document_type_chinese": "出院记录",
      "document_type_english": "Discharge Summary",
      "timestamp": "2025-08-05T10:30:00"
    }
  },
  "timestamp": "2025-08-05T10:30:00"
}
```

**错误响应 (400/500)**:
```json
{
  "success": false,
  "error": {
    "type": "ValidationError",
    "message": "缺少必需参数: type",
    "timestamp": "2025-08-05T10:30:00"
  }
}
```

## 使用示例

### Python示例

```python
import requests
import json

# API配置
API_URL = "http://localhost:9086/hospical/quality_control"

# 出院小结数据示例
discharge_data = {
    "type": "Discharge Summary",
    "data": {
        "Patient": {
            "PatientId": "P001",
            "PatientName": "张三",
            "Age": 45,
            "SexName": "男"
        },
        "VisitInfo": {
            "AdmissionNumber": "A001",
            "DeptName": "内科",
            "AttendingDocName": "李医生",
            "BedNurName": "王护士",
            "ChiefDocName": "陈主任"
        },
        "RepSummaryInfo": {
            "AssayResult": "血常规正常，肝功能正常",
            "IsTumor": "否",
            "TreatmentOutcomes": "好转",
            "Complication": "无",
            "DisHospitalDisease": "患者一般情况良好",
            "DisHospitalDrugs": "口服药物治疗"
        }
    }
}

# 发送请求
try:
    response = requests.post(
        API_URL,
        json=discharge_data,
        headers={"Content-Type": "application/json"},
        timeout=30
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            data = result['data']
            print(f"质控分数: {data['quality_score']['total_score']}/100")
            print(f"质控状态: {data['quality_status']}")
            print(f"质量等级: {data['statistics']['quality_grade']}")
        else:
            print(f"质控失败: {result['error']['message']}")
    else:
        print(f"请求失败: {response.status_code}")
        
except requests.exceptions.RequestException as e:
    print(f"连接失败: {e}")
```

### JavaScript示例

```javascript
// 出院小结数据示例
const dischargeData = {
    type: "Discharge Summary",
    data: {
        Patient: {
            PatientId: "P001",
            PatientName: "张三",
            Age: 45,
            SexName: "男"
        },
        VisitInfo: {
            AdmissionNumber: "A001",
            DeptName: "内科",
            AttendingDocName: "李医生",
            BedNurName: "王护士",
            ChiefDocName: "陈主任"
        },
        RepSummaryInfo: {
            AssayResult: "血常规正常，肝功能正常",
            IsTumor: "否",
            TreatmentOutcomes: "好转",
            Complication: "无",
            DisHospitalDisease: "患者一般情况良好",
            DisHospitalDrugs: "口服药物治疗"
        }
    }
};

// 发送请求
fetch('http://localhost:9086/hospical/quality_control', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify(dischargeData)
})
.then(response => response.json())
.then(result => {
    if (result.success) {
        const data = result.data;
        console.log(`质控分数: ${data.quality_score.total_score}/100`);
        console.log(`质控状态: ${data.quality_status}`);
        console.log(`质量等级: ${data.statistics.quality_grade}`);
    } else {
        console.error(`质控失败: ${result.error.message}`);
    }
})
.catch(error => {
    console.error('连接失败:', error);
});
```

### cURL示例

```bash
# 出院小结质控
curl -X POST http://localhost:9086/hospical/quality_control \
  -H "Content-Type: application/json" \
  -d '{
    "type": "Discharge Summary",
    "data": {
      "Patient": {
        "PatientId": "P001",
        "PatientName": "张三",
        "Age": 45,
        "SexName": "男"
      },
      "VisitInfo": {
        "AdmissionNumber": "A001",
        "DeptName": "内科"
      },
      "RepSummaryInfo": {
        "AssayResult": "血常规正常",
        "IsTumor": "否",
        "TreatmentOutcomes": "好转"
      }
    }
  }'
```

## 错误处理

### 常见错误类型

| 错误类型 | HTTP状态码 | 描述 |
|---------|-----------|------|
| ValidationError | 400 | 请求参数验证失败 |
| QualityControlError | 400 | 质控执行失败 |
| SystemError | 500 | 系统初始化失败 |
| InternalServerError | 500 | 服务器内部错误 |
| NotFoundError | 404 | 资源不存在 |
| MethodNotAllowedError | 405 | 请求方法不允许 |

### 错误响应示例

```json
{
  "success": false,
  "error": {
    "type": "ValidationError",
    "message": "不支持的文档类型: Invalid Type，支持的类型: ['Discharge Summary', 'Initial Progress Note']",
    "timestamp": "2025-08-05T10:30:00"
  }
}
```
